#!/usr/bin/env python
"""
Data Loader Module

This module handles loading product data from various sources,
including JSON files and fallback mock data.
"""

import json
import os
from typing import List, Dict
from datetime import datetime
from crew_components.error_handlers import MappingError


def load_superdrug_products(file_path: str = 'superdrug_batch_4.json', limit: int = 5) -> List[Dict]:
    """
    Load products from superdrug batch JSON file.
    
    Args:
        file_path: Path to the JSON file
        limit: Maximum number of products to load
        
    Returns:
        List of product dictionaries in expected format
        
    Raises:
        MappingError: If file cannot be loaded or parsed
    """
    try:
        # Try multiple possible paths
        possible_paths = [
            file_path,  # Direct path
            os.path.join('..', file_path),  # Parent directory
            os.path.join('../', file_path),  # Parent directory (alternative)
            os.path.abspath(os.path.join(os.path.dirname(__file__), '..', file_path))  # Absolute path to parent
        ]
        
        actual_file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                actual_file_path = path
                break
        
        if actual_file_path is None:
            print(f"Warning: {file_path} not found in any of these locations:")
            for path in possible_paths:
                print(f"  - {os.path.abspath(path)}")
            print("Using fallback mock data.")
            return get_fallback_products()
        
        print(f"Loading products from: {os.path.abspath(actual_file_path)}")
        with open(actual_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            superdrug_items = data.get('items', [])[:limit]
            
            # Convert to expected format
            products_to_categorize = []
            for item in superdrug_items:
                product = {
                    'product_id': item.get('product_id', 'unknown'),
                    'product_name': item.get('name', ''),
                    'product_description': item.get('description', ''),
                    'url': item.get('url', ''),
                    'breadcrumbs': item.get('breadcrumbs', [])
                }
                products_to_categorize.append(product)
                
            print(f"Loaded {len(products_to_categorize)} products from {file_path}")
            return products_to_categorize
            
    except json.JSONDecodeError as e:
        print(f"Error reading {file_path}: {e}")
        raise MappingError(f"Failed to parse product data from {file_path}") from e
    except Exception as e:
        print(f"Unexpected error loading {file_path}: {e}")
        raise MappingError(f"Failed to load product data from {file_path}") from e


def get_fallback_products() -> List[Dict]:
    """
    Get fallback mock product data when main data source is unavailable.
    
    Returns:
        List of mock product dictionaries
    """
    return [
         {
            'product_id': 4832,
            'product_name': "ASDA Garden Scoop",
            'product_description': "This garden scoop is ideal for many tasks both in the garden and around your home",
            'url': 'https://groceries.asda.com/product/garden-tools-accessories/asda-garden-scoop/1000003352998',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'Gardening', 'Garden Tools & Accessories']
        },
        {
            'product_id': 4833,
            'product_name': "Organic Tomato Seeds",
            'product_description': "Premium organic tomato seeds for home gardening",
            'url': 'https://groceries.asda.com/product/seeds/organic-tomato-seeds/1000003352999',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'Gardening', 'Seeds & Bulbs']
        },
        {
            'product_id': 4834,
            'product_name': "Kitchen Knife Set",
            'product_description': "Professional 5-piece kitchen knife set with wooden block",
            'url': 'https://groceries.asda.com/product/kitchen/knife-set/1000003353000',
            'breadcrumbs': ['Home & Entertainment', 'Kitchen & Dining', 'Cooking & Baking', 'Knives & Cutting']
        },
        {
            'product_id': 4835,
            'product_name': "Wireless Bluetooth Headphones",
            'product_description': "High-quality wireless headphones with noise cancellation",
            'url': 'https://groceries.asda.com/product/electronics/headphones/1000003353001',
            'breadcrumbs': ['Electronics', 'Audio & Video', 'Headphones & Earphones']
        },
        {
            'product_id': 4836,
            'product_name': "Baby Formula Milk",
            'product_description': "Nutritious baby formula for infants 0-6 months",
            'url': 'https://groceries.asda.com/product/baby/formula/1000003353002',
            'breadcrumbs': ['Baby & Toddler', 'Baby Food & Milk', 'Baby Formula']
        },
        {
            'product_id': 4837,
            'product_name': "ASDA Garden Hose 15m",
            'product_description': "Durable 15 meter garden hose suitable for watering plants and lawns",
            'url': 'https://groceries.asda.com/product/garden-tools-accessories/asda-garden-hose/1000003353003',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'Gardening', 'Garden Tools & Accessories']
        },
        {
            'product_id': 4838,
            'product_name': "Stainless Steel Saucepan 20cm",
            'product_description': "Premium stainless steel saucepan ideal for everyday cooking",
            'url': 'https://groceries.asda.com/product/kitchen/saucepans/1000003353004',
            'breadcrumbs': ['Home & Entertainment', 'Kitchen & Dining', 'Cooking & Baking', 'Pots & Pans']
        },
        {
            'product_id': 4839,
            'product_name': "LED Desk Lamp",
            'product_description': "Adjustable LED desk lamp with brightness settings for home office",
            'url': 'https://groceries.asda.com/product/home-lighting/led-desk-lamp/1000003353005',
            'breadcrumbs': ['Home & Entertainment', 'Home Accessories', 'Lighting', 'Desk Lamps']
        },
        {
            'product_id': 4840,
            'product_name': "Gaming Mouse",
            'product_description': "Ergonomic gaming mouse with programmable buttons and RGB lighting",
            'url': 'https://groceries.asda.com/product/electronics/computers/gaming-mouse/1000003353006',
            'breadcrumbs': ['Electronics', 'Computers & Accessories', 'Mice & Keyboards']
        },
        {
            'product_id': 4841,
            'product_name': "Infant Diapers Size 2",
            'product_description': "Soft and absorbent nappies for infants weighing 3-6kg",
            'url': 'https://groceries.asda.com/product/baby/nappies/size-2/1000003353007',
            'breadcrumbs': ['Baby & Toddler', 'Nappies & Wipes', 'Nappies']
        },
        {
            'product_id': 4842,
            'product_name': "BBQ Charcoal Briquettes",
            'product_description': "Long-lasting charcoal briquettes for barbecues and grilling",
            'url': 'https://groceries.asda.com/product/garden-bbq/charcoal-briquettes/1000003353008',
            'breadcrumbs': ['Home & Entertainment', 'Garden & Outdoor', 'BBQ & Accessories', 'Charcoal']
        }
    ]


def save_results_to_json(results: List[Dict], output_file: str = None):
    """
    Save mapping results to a JSON file.
    
    Args:
        results: List of result dictionaries
        output_file: Output filename (auto-generated if None)
    """
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"mapping_results_{timestamp}.json"
    
    # Format results for JSON output
    formatted_results = []
    for result in results:
        if result.get('status') == 'success' and result.get('result'):
            product_id = result.get('product_id')
            mapping_result = result.get('result')
            
            # Find the original product data (you'll need to pass this)
            formatted_result = {
                'product_id': product_id,
                'product_name': result.get('product_name', ''),
                'product_description': result.get('product_description', ''),
                'product_url': result.get('product_url', ''),
                'breadcrumbs': result.get('breadcrumbs', []),
                'category_path': {
                    'level_1': mapping_result.get('level_1', ''),
                    'level_2': mapping_result.get('level_2', ''),
                    'level_3': mapping_result.get('level_3', ''),
                    'level_4': mapping_result.get('level_4', ''),
                    'level_5': mapping_result.get('level_5', ''),
                    'level_6': mapping_result.get('level_6', ''),
                    'level_7': mapping_result.get('level_7', '')
                },
                'confidence_score': mapping_result.get('confidence_score', 0.0),
                'processed_at': datetime.now().isoformat()
            }
            formatted_results.append(formatted_result)
    
    # Save to JSON file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_processed': len(results),
                    'successful_mappings': len(formatted_results),
                    'generated_at': datetime.now().isoformat(),
                    'source': 'auto_mapping_system'
                },
                'results': formatted_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n[OUTPUT] Results saved to: {output_file}")
        print(f"[OUTPUT] Successfully mapped: {len(formatted_results)} products")
        
    except Exception as e:
        print(f"[ERROR] Failed to save results to {output_file}: {e}")


def load_products_from_any_source(preferred_file: str = 'superdrug_batch_4.json', limit: int = 5) -> List[Dict]:
    """
    Load products from preferred source with automatic fallback.
    
    Args:
        preferred_file: Preferred JSON file to load from
        limit: Maximum number of products to load
        
    Returns:
        List of product dictionaries
    """
    try:
        return load_superdrug_products(preferred_file, limit)
    except MappingError:
        print("Falling back to mock data due to loading error.")
        return get_fallback_products()[:limit]