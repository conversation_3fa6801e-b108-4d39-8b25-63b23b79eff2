from crewai import Task
from .agents import mapper_agent, validation_agent
from .output_schemas import CategoryMapping, ValidationResult


def get_mapping_task_config():
    """
    Get the mapping task configuration that can be reused.
    
    Returns:
        dict: Task configuration dictionary
    """
    return {
        'description': (
            'Map the product to the most accurate category path that EXISTS in the database.\n'
            'Product Info: {product_info}.\n'
            '{retry_feedback}'  # This will contain feedback on previous failed attempts.

            'PRODUCT PRIORITY: '
            '1. PRODUCT NAME IS THE HIGHEST PRIORITY. ' \
            '2. BREADCRUMBS ARE THE SECOND PRIORITY. ' \
            '3. URL IS THE THIRD PRIORITY. ' \
            '4. DESCRIPTION IS THE FOURTH PRIORITY. ' \

            'CRITICAL RULES: ' 
            '2. You MUST use the category_semantic_search tool to find valid database categories ' 
            '4. If you receive feedback about a previous failed attempt, learn from it and correct your approach. '
            '5. If no categories are found after multiple search attempts, use a different priority product info and try again. '
            '6. Do NOT copy breadcrumbs directly. Use it to understand the product and create a search query. '
            '7. Create a search query from the product info, use the search tool, and select the best match from the results. '
            '8. If you are not completely confident in the search results, do NOT make up a category. Return an empty JSON object instead. \n\n'

            'TOOL USAGE INSTRUCTIONS:\n'
            'When calling category_semantic_search, pass ONLY a simple string query:\n\n'

            'CORRECT examples:\n'
            '- category_semantic_search({"query": "organic tomato seeds"})\n'
            '- category_semantic_search({"query": "garden tools"})\n\n'

            'SEARCH STRATEGY (PRIORITIZE PRODUCT NAME):\n'
            '1. FIRST: Use the exact product name or key words from product name (e.g., "tomato seeds" for "Organic Tomato Seeds")\n'
            '2. SECOND: Try broader category terms if no results (e.g., "seeds" or "vegetable seeds")\n'
            '3. THIRD: Try one more specific variation\n'
            '4. LAST RESORT: Switch priority by using different product info as fallback and repeat the process\n\n\n'

            'FINAL OUTPUT REQUIREMENT:\n'
            'After using the search tool, you MUST return ONLY the JSON object with category levels. '
            'Do NOT return tool calls, search results, or explanations in your final answer. '
            'ONLY return the JSON category mapping object.'

        ),
        'expected_output': (
            'Return ONLY a valid JSON object in this exact format:\n'
            '{\n'
            '  "level_1": "Category Name",\n'
            '  "level_2": "Subcategory Name",\n'
            '  "level_3": "Sub-subcategory Name",\n'
            '  "level_4": "",\n'
            '  "level_5": "",\n'
            '  "level_6": "",\n'
            '  "level_7": ""\n'
            '}\n'
            'CRITICAL: Use empty strings "" for unused levels. Do NOT include tool calls, explanations, or any other text. '
            'ONLY return the JSON object above with actual category names from search results.'
        ),
        'output_pydantic': CategoryMapping
    }


def get_validation_task_config():
    """
    Get the validation task configuration that can be reused.
    
    Returns:
        dict: Task configuration dictionary
    """
    return {
        'description': (
            'Review the MAPPER AGENT\'S PROPOSED CATEGORY PATH and provide the FINAL category mapping. '
            'You are responsible for delivering the final, verified category path for the product.\n'
            'Product Info: {product_info}\n'
            'Mapper Output: {mapper_output}\n'
            '{retry_feedback}'  # This will contain feedback on previous failed attempts.
            '\n'

            'CRITICAL VALIDATION RULES: '
            '1. Review the MAPPER AGENT\'S proposed category path provided in the mapper_output '
            '2. Use the search tool to verify the mapper\'s categories exist in the database '
            '3. If the mapper\'s path is accurate and exists in database, use it as your final output '
            '4. If the mapper\'s path has issues, find the correct category path using search tool '
            '5. If you receive retry feedback, CAREFULLY follow its instructions to avoid repeating mistakes '
            '6. ALWAYS provide a final category mapping - never reject without providing an alternative '
            '7. LIMIT search attempts to maximum 5 queries total '
            '8. ONLY use exact category paths from search tool results - DO NOT create new categories '
            '9. Focus on finding the most accurate category path for the product '
            '10. Ensure at least level_1 is populated in your final output '
            '11. Use search strategy: product name → broader terms → product type → fallback options\n\n'

            'TOOL USAGE INSTRUCTIONS:\n'
            'When calling category_semantic_search, pass ONLY a simple string query:\n\n'

            'CORRECT examples:\n'
            '- category_semantic_search({"query": "garden tools"})\n'
            '- category_semantic_search({"query": "organic tomato seeds"})\n'
        ),
        'expected_output': (
            'Return ONLY a valid JSON object in this exact format:\n'
            '{\n'
            '  "level_1": "Category Name",\n'
            '  "level_2": "Subcategory Name",\n'
            '  "level_3": "Sub-subcategory Name",\n'
            '  "level_4": "",\n'
            '  "level_5": "",\n'
            '  "level_6": "",\n'
            '  "level_7": "",\n'
            '  "confidence_score": 0.95\n'
            '}\n'
            'CRITICAL: Use empty strings "" for unused levels. Provide confidence_score between 0.0 and 1.0. '
            'Do NOT include explanations or any other text. ONLY return the JSON object with actual category names.'
            'If you receive retry feedback, CAREFULLY follow its instructions to avoid repeating mistakes'
        ),
        'output_pydantic': ValidationResult
    }


def create_mapping_task(agent, context=None):
    """
    Create a mapping task with the standard configuration.
    
    Args:
        agent: The agent to assign to this task
        context: Optional context tasks (list)
    
    Returns:
        Task: Configured mapping task
    """
    config = get_mapping_task_config()
    return Task(
        description=config['description'],
        expected_output=config['expected_output'],
        agent=agent,
        output_pydantic=config['output_pydantic'],
        context=context or []
    )


def create_validation_task(agent, mapping_task=None):
    """
    Create a validation task with the standard configuration.
    
    Args:
        agent: The agent to assign to this task
        mapping_task: Optional mapping task to use as context
    
    Returns:
        Task: Configured validation task
    """
    config = get_validation_task_config()
    return Task(
        description=config['description'],
        expected_output=config['expected_output'],
        agent=agent,
        context=[mapping_task] if mapping_task else [],
        output_pydantic=config['output_pydantic']
    )


def create_standalone_validation_task(agent):
    """
    Create a standalone validation task that accepts mapper_output as input.
    
    Args:
        agent: The agent to assign to this task
    
    Returns:
        Task: Configured validation task without context dependency
    """
    config = get_validation_task_config()
    return Task(
        description=config['description'],
        expected_output=config['expected_output'],
        agent=agent,
        context=[],  # No context dependency
        output_pydantic=config['output_pydantic']
    )

# Create the original tasks using the factory functions
map_product_task = create_mapping_task(mapper_agent)
validate_mapping_task = create_validation_task(validation_agent, map_product_task)
