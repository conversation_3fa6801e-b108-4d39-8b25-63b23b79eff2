from crewai import Agent
from crewai_tools import PGSearchTool

# The search tool will be configured in auto_mapping.py
# We keep this as placeholder for now
search_tool = None

# Mapper Agent
mapper_agent = Agent(
    role='Product Category Mapper',
    goal='Accurately map products to a 7-level category hierarchy based on product information.',
    backstory=(
        'You are an expert in product categorization. Your task is to analyze product data, '
        'including names, descriptions, and any available breadcrumbs or URL paths, to determine the most '
        'fitting category for each product within a complex, multi-level hierarchy. '
        'You prioritize clear signals like breadcrumbs to work efficiently and reduce costs.'
    ),
    tools=[],  # Tools will be set in main script
    allow_delegation=False,
    max_iter=10,
    verbose=True
)

# Validation Agent
validation_agent = Agent(
    role='Mapping Validation Specialist',
    goal='Verify the accuracy of product category mappings and provide corrected paths when necessary.',
    backstory=(
        'You are a meticulous quality assurance expert with a deep understanding of product taxonomies. '
        'Your role is to act as a second opinion, reviewing the work of the mapping agent. '
        'You double-check if the proposed category is the best fit. If you find a mistake, you use your '
        'own search capabilities to find and propose a more accurate, valid category path.'
    ),
    tools=[],  # Tools will be set in main script
    allow_delegation=False,
    max_iter=10,
    verbose=True
)
