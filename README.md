# AI-Powered Product Category Mapping System

An enterprise-grade, intelligent multi-agent system for automatically mapping products to a 7-level category hierarchy. Built with CrewAI agents, FAISS vector search, and FastAPI integration, providing scalable, accurate category mapping with comprehensive error handling and performance optimization.

## 🌟 Key Features

- **🤖 AI-Powered Mapping**: CrewAI agents with specialized roles (Mapper + Validation)
- **🔍 Semantic Search**: Advanced FAISS vector search for intelligent category matching
- **⚡ High Performance**: Async processing with concurrent execution (1.24 products/second)
- **🛡️ Enterprise Ready**: Comprehensive error handling, logging, and monitoring
- **🔄 Scalable Architecture**: FastAPI backend with database integration
- **📊 Performance Optimized**: FAISS index optimization (7ms-58ms search times)

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    AI Category Mapping System                  │
├─────────────────────────────────────────────────────────────────┤
│  🎯 Entry Points                                               │
│  ├── auto_mapping.py           # Direct processing             │
│  ├── async_processor.py        # Async batch processing        │
│  └── backend/routes.py         # FastAPI REST API              │
├─────────────────────────────────────────────────────────────────┤
│  🤖 CrewAI Intelligence Layer                                  │
│  ├── Mapper Agent              # Product → Category Mapping    │
│  ├── Validation Agent          # Quality Assurance & Validation│
│  ├── Per-Level Validation      # Embeddings-based existence check│
│  └── Task Orchestration        # Sequential Processing Flow    │
├─────────────────────────────────────────────────────────────────┤
│  🔍 Search & Retrieval Engine                                  │
│  ├── FAISS Vector Search       # Semantic similarity (21K+)    │
│  ├── OpenAI Embeddings         # text-embedding-3-small model  │
│  └── Similarity Scoring        # Advanced vector matching      │
├─────────────────────────────────────────────────────────────────┤
│  ⚡ Performance & Reliability                                   │
│  ├── Async Processing          # Concurrent execution          │
│  ├── Retry Logic              # Exponential backoff           │
│  ├── Error Handling           # Comprehensive recovery         │
│  └── Performance Monitoring   # Real-time metrics             │
├─────────────────────────────────────────────────────────────────┤
│  🗄️ Data Layer                                                 │
│  ├── PostgreSQL Database      # Category hierarchy (3,424)     │
│  ├── FAISS Embeddings         # Pre-computed vectors (53MB)    │
│  └── Structured Logging       # Performance & error tracking   │
└─────────────────────────────────────────────────────────────────┘
```

### Directory Structure

```
category-mapper/                     # Project root
├── .env                            # Environment configuration (root level)
└── auto-mapping/                   # Main system directory
    ├── 📄 Core Processing
    │   ├── auto_mapping.py              # Main entry point & demo
    │   ├── async_processor.py           # Async processing engine
    │   └── data_loader.py               # Multi-source data loading
    ├── 🤖 CrewAI Components
    │   └── crew_components/
    │       ├── agents.py                # Mapper & Validation agents
    │       ├── tasks.py                 # CrewAI task definitions
    │       ├── error_handlers.py        # Enterprise error handling
    │       ├── logging_config.py        # Structured logging system
    │       ├── cost_tracker.py          # Cost monitoring & estimation
    │       └── tools/                   # AI search capabilities
    │           ├── category_semantic_search.py    # FAISS vector search
    │           └── category_embeddings_manager.py # Embedding operations
    ├── 🔄 API & Backend
    │   └── backend/
    │       ├── routes.py                # FastAPI REST endpoints
    │       ├── database.py              # Database operations
    │       └── test_integration.py      # Backend integration tests
    ├── 📊 Data & Performance
    │   ├── embeddings/                  # FAISS indices & metadata
    │   ├── logs/                        # Structured application logs
    │   └── docs/                        # Comprehensive documentation
    └── 🛠️ Configuration
        ├── requirements.txt             # Python dependencies
        └── environment_template.txt     # Environment setup guide
```

## 🚀 Quick Start

### Prerequisites
- **Python 3.9+** with pip
- **OpenAI API key** for AI processing and embeddings
- **8GB+ RAM** recommended for FAISS embeddings
- **53MB disk space** for category embeddings storage

### 1. Installation & Setup

```bash
# Clone and navigate to project root
cd category-mapper/

# Navigate to auto-mapping directory for Python setup
cd auto-mapping/

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Return to project root for .env configuration
cd ..
```

### 2. Environment Configuration

**Core Requirements**: Only OpenAI API key needed for full functionality.

```bash
# Copy template and configure (from project root)
cp auto-mapping/environment_template.txt .env

# Edit .env with your credentials:
# Required:
OPENAI_API_KEY=your_openai_key_here

# Optional performance tuning:
BATCH_SIZE=100
MAX_RETRIES=3
LOG_LEVEL=INFO
EMBEDDINGS_DIR=embeddings/  # Custom embeddings location
```

### 3. Initialize Embeddings (One-time Setup)

```bash
# Navigate to auto-mapping directory (if not already there)
cd auto-mapping/

# Generate category embeddings (~$0.0014 cost, ~2 minutes)
python generate_embeddings.py

# Verify embeddings were created
ls -la embeddings/
# Should show: category_embeddings.npz, category_metadata.json, embeddings.faiss
```

### 4. Run the System

**Option A: Demo Processing (Single product)**
```bash
# From auto-mapping directory
python auto_mapping.py
```

**Option B: FastAPI Server**
```bash
# From auto-mapping directory, start the API server
cd backend/
uvicorn routes:app --reload --port 8000

# Test the API
curl -X POST "http://localhost:8000/get_product_mapping" \
  -H "Content-Type: application/json" \
  -d '{"products": [{"product_id": 1, "title": "Organic Tomato Seeds", "description": "Premium seeds for gardening"}]}'
```

**Option C: Async Batch Processing**
```bash
# From auto-mapping directory, process products from superdrug_batch_4.json
python async_processor.py
```

## 🎯 Core Capabilities

### 🔍 Enhanced Validation System (New!)

Our latest validation improvements provide unprecedented accuracy and error reporting:

- **Per-Level Validation**: Each category level (level_1 through level_7) is individually verified against the embeddings database
- **Exact Segment Matching**: Uses semantic search to verify each category name exists as an exact segment in valid category paths
- **Comprehensive Error Reporting**: Accumulates all validation issues and reports them together for efficient correction
- **Gap Detection**: Identifies missing intermediate levels (e.g., empty level_2 when level_3 is provided)
- **Memory-Enhanced Retries**: Agents retain memory from previous attempts, learning from validation failures
- **Focused Scope**: Validation strictly verifies existence without overstepping into analysis or suggestions

**Example Validation Errors**:
- `"Category level_1 is empty; Category level_4 does not exist: 'Blusher'"`
- `"Category level_2 is empty while deeper levels are provided"`
- `"Category level_3 does not exist: 'NonexistentCategory'"`

### 🤖 AI-Powered Agents
- **Mapper Agent**: Specialized in product analysis and category mapping
- **Validation Agent**: Quality assurance with embeddings-based validation
- **Sequential Processing**: Two-stage validation ensures high accuracy
- **Retry Logic**: Exponential backoff with intelligent feedback and memory retention
- **Enhanced Validation**: Per-level category existence verification with detailed error reporting

### 🔍 Advanced Search Engine
- **Semantic Search**: FAISS vector similarity (21,000+ embeddings)
- **OpenAI Embeddings**: text-embedding-3-small model for high-quality vectors
- **Vector Similarity**: Cosine similarity scoring for accurate matches
- **Performance**: 7ms-58ms search times (index-dependent)

### ⚡ Performance & Scalability
- **Async Processing**: Concurrent execution up to 3 products simultaneously
- **Throughput**: 1.24 products/second (end-to-end with validation)
- **FAISS Optimization**: Multiple index types (Flat, IVF, HNSW)
- **Memory Optimization**: Efficient FAISS index management

### 🛡️ Enterprise-Grade Reliability
- **Comprehensive Error Handling**: Categorized error recovery
- **Structured Logging**: Performance metrics and detailed audit trails
- **Cost Tracking**: Real-time monitoring of OpenAI API usage
- **Health Monitoring**: System status indicators and diagnostics
- **Advanced Validation**: Embeddings-based category existence verification
- **Smart Retry System**: Memory-enabled agents learn from previous attempts

## 📊 Performance Metrics

### Current Benchmarks
- **🎯 Throughput**: 1.24 products/second (full pipeline)
- **🔍 Search Performance**: 7ms (HNSW) to 58ms (Flat) average
- **🗄️ Database**: 3,424 unique category paths
- **💾 Storage**: 53MB total (embeddings + indices)

### Optimization Results
- **⚡ Parallel Speedup**: 1.26x faster (20.8% time savings)
- **🏗️ FAISS Optimization**: 6 index types available
- **💰 Cost Efficiency**: ~$0.0014 one-time embedding generation
- **🔄 Async Benefits**: 3x concurrent processing capability

### Resource Usage
- **RAM**: 8GB+ recommended for optimal FAISS performance
- **CPU**: Multi-core beneficial for parallel processing
- **Network**: Minimal after initial embedding generation
- **API Costs**: Variable based on product volume (~$0.001-0.01 per product)

## 🛠️ Development & Integration

### 🧪 Testing the System

**Unit Testing**
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov --cov-report=html

# Test specific components
pytest tests/unit/test_agents.py
pytest tests/integration/test_backend.py
```

**Search Tool Testing**
```python
from crew_components.tools import CategorySemanticSearchTool

# Initialize search tool
search_tool = CategorySemanticSearchTool()

# Test semantic search
results = search_tool._run("organic garden seeds for tomatoes")
print(results)

# Example output:
# Search results for query: 'organic garden seeds for tomatoes'
# --------------------------------------
#   1. Path: "Garden & DIY > Garden Plants, Seeds & Bulbs > Seeds > Vegetable Seeds" (Score: 0.8654)
#   2. Path: "Garden & DIY > Garden Plants, Seeds & Bulbs > Seeds > Herb Seeds" (Score: 0.7892)
```

### 🔄 FastAPI Integration

**API Endpoints**
- `POST /get_product_mapping` - Synchronous processing
- `POST /get_product_mapping_async` - Asynchronous processing
- `GET /health` - System health check

**Request Format**
```json
{
  "products": [
    {
      "product_id": 12345,
      "title": "Organic Tomato Seeds Premium",
      "description": "High-quality heirloom tomato seeds for home gardening"
    }
  ]
}
```

**Response Format**
```json
{
  "status": "Success",
  "message": "Successfully mapped categories using CrewAI.",
  "data": [
    {
      "product_id": 12345,
      "level_1": "Garden & DIY",
      "level_2": "Garden Plants, Seeds & Bulbs",
      "level_3": "Seeds",
      "level_4": "Vegetable Seeds",
      "level_5": "",
      "level_6": "",
      "level_7": "",
      "confidence_score": 0.95,
      "cost_info": {
        "total_cost": 0.002,
        "total_tokens": 1500,
        "processing_time": 2.3,
        "retry_attempts": 0
      }
    }
  ]
}
```

### 🔧 Advanced Configuration

**FAISS Index Optimization**
```python
# Configure different index types for performance tuning
# Edit embeddings_manager.py or use environment variables:

# Fastest (7ms avg): HNSW
FAISS_INDEX_TYPE=HNSW

# Memory efficient: IVFPQ  
FAISS_INDEX_TYPE=IVFPQ

# High accuracy: Flat (default)
FAISS_INDEX_TYPE=Flat
```

**Parallel Processing**
```bash
# Adjust concurrent workers in async_processor.py
MAX_CONCURRENT=3  # Default
MAX_RETRIES=2     # Default
```

## 📝 Data Schemas & Workflows

### Input Product Schema
```python
{
    "product_id": 12345,                    # Required: Unique identifier
    "title": "Organic Tomato Seeds",        # Required: Product name
    "description": "Premium seeds...",      # Required: Product description
    "breadcrumbs": "Garden > Seeds",        # Optional: Existing categorization
    "url": "https://store.com/product",     # Optional: Product URL
    "brand": "GardenPro",                   # Optional: Brand information
    "price": 4.99                           # Optional: Price information
}
```

### Processing Workflow
```
📥 Input Product
     ↓
🔍 Semantic Search (FAISS Embeddings)
     ↓
🤖 Mapper Agent Analysis
     ↓ (explicit passthrough)
🔍 Validation Agent Review
     ↓
✅ Per-Level Category Validation (NEW!)
     ↓ (retry with memory if validation fails)
📊 Confidence Scoring
     ↓
📤 Category Path Output
```

### Output Response Schema
```python
{
    "product_id": 12345,
    "level_1": "Garden & DIY",              # Top-level category
    "level_2": "Garden Plants, Seeds & Bulbs",
    "level_3": "Seeds",
    "level_4": "Vegetable Seeds",
    "level_5": "",                          # May be empty
    "level_6": "",                          # May be empty
    "level_7": "",                          # May be empty
    "confidence_score": 0.95,               # AI confidence (0.0-1.0)
    "processing_time": 2.3,                 # Seconds
    "retry_attempts": 0,                    # Number of retries
    "cost_tracking": {                      # Optional cost information
        "total_cost": 0.002,
        "total_tokens": 1500
    }
}
```

### Error Response Schema
```python
{
    "product_id": 12345,
    "status": "error",
    "error_message": "Search tool initialization failed",
    "error_category": "TOOL",
    "retry_attempts": 2,
    "processing_time": 1.2
}
```

## 🔧 Troubleshooting

### 💾 Storage & File Issues
**Problem**: Embeddings not found or corrupted files
```bash
# Check embeddings directory structure
ls -la embeddings/
# Should show: category_embeddings.npz, category_metadata.json, embeddings.faiss

# Check file sizes
du -h embeddings/*
# Expected: ~20MB for embeddings.faiss, ~30MB for category_embeddings.npz
```

**Solution**: 
- Regenerate embeddings: `python generate_embeddings.py`
- Verify sufficient disk space (53MB minimum)
- Check file permissions and directory access

### 🧠 Embeddings & AI Issues
**Problem**: "Embeddings not available" or search failures
```bash
# Check embeddings directory
ls -la embeddings/
# Should show: category_embeddings.npz, category_metadata.json, embeddings.faiss

# Verify OpenAI API key
python -c "import openai; print('API key configured')"
```

**Solution**:
- Regenerate embeddings: `python generate_embeddings.py`
- Verify OPENAI_API_KEY in `.env`
- Check available disk space (53MB required)

### ⚡ Performance Issues
**Problem**: Slow processing or high memory usage
```bash
# Monitor system resources
htop
df -h  # Check disk space
```

**Solution**:
- Reduce `MAX_CONCURRENT` in async_processor.py
- Use HNSW index for faster FAISS search (7ms vs 58ms)
- Increase system RAM or reduce batch sizes

### 🤖 Agent Execution Errors
**Problem**: CrewAI agents failing or producing invalid results

**Common Issues**:
- **Tool Format Errors**: Agents learning tool input format (normal, retries help)
- **Context Length**: Reduce product description length if hitting token limits
- **API Rate Limits**: Built-in retry logic handles temporary rate limits
- **Category Validation Failures**: New per-level validation catches non-existent categories

**Solution**:
- Check logs in `logs/` directory for detailed error information
- Verify agent prompts in `crew_components/agents.py`
- Increase `MAX_RETRIES` for unstable network conditions
- **New**: Validation errors now provide specific level information (e.g., "level_4 does not exist: 'Blusher'")

### 🔄 API & FastAPI Issues
**Problem**: Server startup failures or endpoint errors
```bash
# Test server startup
cd backend/
uvicorn routes:app --reload --port 8000

# Test health endpoint
curl http://localhost:8000/health
```

**Solution**:
- Check all dependencies installed: `pip install -r requirements.txt`
- Verify embeddings are generated and accessible
- Check port availability: `netstat -ln | grep 8000`

## 📚 Architecture Deep Dive

### 🔍 Search Engine Components

**CategorySemanticSearchTool**
- FAISS-powered vector similarity search
- 21,000+ pre-computed category embeddings
- Multiple index types (Flat, IVF, HNSW) for performance tuning
- Semantic understanding of product context

**CategoryEmbeddingsManager** 
- OpenAI text-embedding-3-small model integration
- Batch embedding generation and storage
- FAISS index management and optimization
- Statistics and health monitoring

**Semantic Search Strategy**
- Pure vector similarity using cosine distance
- Advanced FAISS indexing for sub-50ms search times
- Intelligent relevance scoring and ranking
- Automatic fallback for edge cases and empty queries

### 🤖 AI Agent Architecture

**Mapper Agent**
- Specialized in product analysis and categorization
- Utilizes product title, description, and breadcrumbs
- Implements cost-efficient search strategies
- Produces structured 7-level category paths

**Validation Agent**
- Quality assurance and error correction
- Independent verification of mapper results
- Confidence scoring and path refinement
- Handles edge cases and ambiguous products

**Processing Flow**
- Explicit passthrough design (Mapper → Validation)
- No shared context dependencies
- Retry logic with exponential backoff
- Comprehensive error handling and recovery

### 🚦 System Status Indicators

- **`[OK]`** - Component initialized successfully
- **`[WARNING]`** - Running with reduced capabilities  
- **`[ERROR]`** - Component failed to initialize
- **`[RETRY]`** - Attempting recovery/retry operation

### 📊 Monitoring & Observability

**Structured Logging**
- Performance metrics tracking
- Error categorization and analysis
- Cost tracking and optimization
- Detailed audit trails

**Health Monitoring**
- FAISS index status verification
- API endpoint health validation
- Resource usage monitoring
- Embedding file integrity checks

## 🤝 Contributing

### Development Workflow
1. **Fork & Clone**: Standard GitHub workflow
2. **Environment Setup**: Follow quick start guide
3. **Testing**: Run `pytest` before submitting PRs
4. **Documentation**: Update relevant docs with changes
5. **Performance**: Use `python analyze_performance.py` for benchmarking

### Code Standards
- **Type Hints**: Required for all functions
- **Docstrings**: Google style for classes and functions
- **Error Handling**: Use structured error patterns
- **Testing**: Maintain >70% code coverage
- **Performance**: Profile performance-critical changes

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support & Documentation

### Additional Resources
- **[Complete API Reference](auto-mapping/docs/api_reference.md)** - Detailed API documentation
- **[Agent Configuration](auto-mapping/docs/agents.md)** - CrewAI agent setup and customization
- **[Performance Tuning](auto-mapping/docs/performance.md)** - Optimization strategies
- **[Testing Guide](auto-mapping/docs/testing.md)** - Test suite and coverage details

### Getting Help
- **Issues**: Use GitHub Issues for bug reports and feature requests
- **Performance**: Run built-in performance analysis tools
- **Documentation**: Check `/docs` directory for detailed guides
- **Logs**: Review `/logs` directory for troubleshooting information

---

**🚀 Built with cutting-edge AI technology: CrewAI, FAISS, OpenAI, and FastAPI**