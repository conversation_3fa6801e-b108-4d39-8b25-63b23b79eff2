[pytest]
# Test discovery patterns
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Test directories
testpaths = tests

# Minimum Python version
minversion = 6.0

# Add source directory to Python path
pythonpath = .

# Output options
addopts = 
    -v
    --strict-markers
    --tb=short
    --disable-warnings
    --cov=crew_components
    --cov=batch_processor
    --cov=auto_mapping
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-config=.coveragerc

# Custom markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    database: Tests requiring database connection
    mock: Tests using mocked dependencies
    network: Tests requiring network access

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s - %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Asyncio configuration
asyncio_mode = auto

# Disable cache for cleaner test runs (optional)
# cache_dir = .pytest_cache

# Environment variables for testing
env_files = 
    .env.test

