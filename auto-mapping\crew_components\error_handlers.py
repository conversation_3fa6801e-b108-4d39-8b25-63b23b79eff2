"""
Error Handlers and Wrappers for Safe Execution

This module provides decorators and wrappers for safe execution of
agent, tool, and database operations with comprehensive error handling.
"""

import functools
import time
from typing import Any, Callable, Dict, Optional, TypeVar, Union
from .logging_config import get_logger, ErrorCategory, LogLevel

# Type variable for generic function returns
T = TypeVar('T')

# Global logger instance
logger = get_logger()


class MappingError(Exception):
    """Base exception for category mapping errors"""
    pass


class ToolExecutionError(MappingError):
    """Exception raised when a tool fails to execute"""
    pass


class AgentExecutionError(MappingError):
    """Exception raised when an agent fails to execute"""
    pass


class DatabaseError(MappingError):
    """Exception raised for database-related errors"""
    pass


class APIError(MappingError):
    """Exception raised for API-related errors"""
    pass


def safe_execution(
    operation_name: str,
    category: ErrorCategory,
    product_id: Optional[int] = None,
    agent_name: Optional[str] = None,
    tool_name: Optional[str] = None,
    default_return: Any = None,
    raise_on_error: bool = False
):
    """
    Decorator for safe execution of functions with error handling and logging.
    
    Args:
        operation_name: Name of the operation for logging
        category: Error category for classification
        product_id: Product being processed
        agent_name: Agent performing the operation
        tool_name: Tool being used
        default_return: Default value to return on error
        raise_on_error: Whether to re-raise exceptions after logging
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            start_time = time.time()
            
            try:
                # Log operation start
                logger.log_event(
                    LogLevel.DEBUG,
                    f"Starting {operation_name}",
                    category=category,
                    product_id=product_id or kwargs.get('product_id'),
                    agent_name=agent_name,
                    tool_name=tool_name,
                    function=func.__name__
                )
                
                # Execute the function
                result = func(*args, **kwargs)
                
              
                
                return result
                
            except Exception as e:
                # Calculate duration even for failures
                duration_ms = (time.time() - start_time) * 1000
                
                # Log the error with full context
                logger.log_error(
                    e,
                    f"Failed to execute {operation_name}",
                    category=category,
                    product_id=product_id or kwargs.get('product_id'),
                    agent_name=agent_name,
                    tool_name=tool_name,
                    duration_ms=duration_ms,
                    function=func.__name__,
                    args=str(args)[:500],  # Truncate to avoid huge logs
                    kwargs=str(kwargs)[:500]
                )
                
                
                if raise_on_error:
                    # Re-raise with context
                    if category == ErrorCategory.TOOL:
                        raise ToolExecutionError(f"{operation_name} failed: {str(e)}") from e
                    elif category == ErrorCategory.AGENT:
                        raise AgentExecutionError(f"{operation_name} failed: {str(e)}") from e
                    elif category == ErrorCategory.DATABASE:
                        raise DatabaseError(f"{operation_name} failed: {str(e)}") from e
                    elif category == ErrorCategory.API:
                        raise APIError(f"{operation_name} failed: {str(e)}") from e
                    else:
                        raise MappingError(f"{operation_name} failed: {str(e)}") from e
                
                # Return default value if not raising
                return default_return
                
        return wrapper
    return decorator


def safe_tool_execution(tool_name: str, product_id: Optional[int] = None):
    """
    Decorator specifically for tool executions.
    
    Args:
        tool_name: Name of the tool being executed
        product_id: Product being processed
    """
    return safe_execution(
        operation_name=f"Tool:{tool_name}",
        category=ErrorCategory.TOOL,
        tool_name=tool_name,
        product_id=product_id,
        raise_on_error=False
    )


def safe_agent_execution(agent_name: str, product_id: Optional[int] = None):
    """
    Decorator specifically for agent executions.
    
    Args:
        agent_name: Name of the agent being executed
        product_id: Product being processed
    """
    return safe_execution(
        operation_name=f"Agent:{agent_name}",
        category=ErrorCategory.AGENT,
        agent_name=agent_name,
        product_id=product_id,
        raise_on_error=False
    )


def safe_database_operation(operation: str, product_id: Optional[int] = None):
    """
    Decorator specifically for database operations.
    
    Args:
        operation: Database operation being performed
        product_id: Product being processed
    """
    return safe_execution(
        operation_name=f"DB:{operation}",
        category=ErrorCategory.DATABASE,
        product_id=product_id,
        raise_on_error=True  # Database errors should typically be raised
    )


def safe_api_call(api_name: str, product_id: Optional[int] = None):
    """
    Decorator specifically for API calls.
    
    Args:
        api_name: Name of the API being called
        product_id: Product being processed
    """
    return safe_execution(
        operation_name=f"API:{api_name}",
        category=ErrorCategory.API,
        product_id=product_id,
        raise_on_error=False
    )


class ProtectedExecution:
    """
    Context manager for protected execution blocks with logging.
    
    Usage:
        with ProtectedExecution("operation_name", product_id=123) as protected:
            # Your code here
            result = do_something()
            protected.set_result(result)
    """
    
    def __init__(self, 
                 operation_name: str,
                 category: ErrorCategory = ErrorCategory.UNKNOWN,
                 product_id: Optional[int] = None,
                 agent_name: Optional[str] = None,
                 tool_name: Optional[str] = None,
                 raise_on_error: bool = False):
        """Initialize protected execution context."""
        self.operation_name = operation_name
        self.category = category
        self.product_id = product_id
        self.agent_name = agent_name
        self.tool_name = tool_name
        self.raise_on_error = raise_on_error
        self.start_time = None
        self.result = None
        self.error = None
        
    def __enter__(self):
        """Enter the protected context."""
        self.start_time = time.time()
        
        logger.log_event(
            LogLevel.DEBUG,
            f"Starting protected {self.operation_name}",
            category=self.category,
            product_id=self.product_id,
            agent_name=self.agent_name,
            tool_name=self.tool_name
        )
        
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the protected context, handling any exceptions."""
        duration_ms = (time.time() - self.start_time) * 1000
        
        if exc_type is None:
            # Successful execution
            pass
        else:
            # Error occurred
            self.error = exc_val
            
            logger.log_error(
                exc_val,
                f"Failed in protected {self.operation_name}",
                category=self.category,
                product_id=self.product_id,
                agent_name=self.agent_name,
                tool_name=self.tool_name,
                duration_ms=duration_ms
            )
            
            
            # Suppress exception if not raising
            if not self.raise_on_error:
                return True  # Suppress the exception
                
        return False  # Don't suppress exceptions if raise_on_error is True
        
    def set_result(self, result: Any):
        """Set the result of the protected operation."""
        self.result = result


def wrap_tool_call(tool_instance, method_name: str = "_run"):
    """
    Wrap a tool's execution method with error handling.
    
    Args:
        tool_instance: The tool instance to wrap
        method_name: The method to wrap (default: "_run")
    
    Returns:
        The wrapped tool instance
    """
    original_method = getattr(tool_instance, method_name)
    
    @functools.wraps(original_method)
    def wrapped_method(*args, **kwargs):
        tool_name = getattr(tool_instance, 'name', tool_instance.__class__.__name__)
        
        with ProtectedExecution(
            f"tool_call:{tool_name}",
            category=ErrorCategory.TOOL,
            tool_name=tool_name,
            raise_on_error=False
        ) as protected:
            result = original_method(*args, **kwargs)
            protected.set_result(result)
            return result
            
    setattr(tool_instance, method_name, wrapped_method)
    return tool_instance


def wrap_agent_execution(agent_instance):
    """
    Wrap an agent's execution with error handling.
    
    Args:
        agent_instance: The agent instance to wrap
    
    Returns:
        The wrapped agent instance
    """
    if hasattr(agent_instance, 'execute'):
        original_execute = agent_instance.execute
        
        @functools.wraps(original_execute)
        def wrapped_execute(*args, **kwargs):
            agent_name = getattr(agent_instance, 'role', agent_instance.__class__.__name__)
            
            with ProtectedExecution(
                f"agent_execution:{agent_name}",
                category=ErrorCategory.AGENT,
                agent_name=agent_name,
                raise_on_error=False
            ) as protected:
                result = original_execute(*args, **kwargs)
                protected.set_result(result)
                return result
                
        agent_instance.execute = wrapped_execute
        
    return agent_instance

 
