[run]
source = .
omit = 
    */tests/*
    */test_*.py
    */__pycache__/*
    */venv/*
    */site-packages/*
    setup.py
    */migrations/*

[report]
exclude_lines =
    # Standard pragma
    pragma: no cover
    
    # Debug-only code
    def __repr__
    def __str__
    
    # Defensive programming
    raise AssertionError
    raise NotImplementedError
    
    # Non-runnable code
    if __name__ == .__main__.:
    if TYPE_CHECKING:
    @abstractmethod
    
    # Logging
    logger.debug
    logging.debug

precision = 2
show_missing = True
skip_covered = False

[html]
directory = htmlcov

[xml]
output = coverage.xml

