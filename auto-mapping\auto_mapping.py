#!/usr/bin/env python
"""
Auto Mapping Main Script

Streamlined async-only category mapping system using CrewAI agents.
Features explicit passthrough processing with retry logic and 
concurrent execution for optimal performance.

Key capabilities:
- Async processing for all product volumes (single or batch)
- Explicit mapper output → validation input flow
- Retry logic with exponential backoff
- Comprehensive error handling and logging
"""

from dotenv import load_dotenv

from crew_components.error_handlers import MappingError
from crew_components.logging_config import get_logger, ErrorCategory, LogLevel
from data_loader import load_products_from_any_source, save_results_to_json 
from async_processor import run_async_processing_with_search_tool

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger()







def main():
    """Main execution function using async processing for all products."""

    try:
        # Log system startup
        logger.log_event(
            LogLevel.INFO,
            "Category mapping system starting",
            category=ErrorCategory.CONFIGURATION
        )
        
        # Load products from data source
        products_to_categorize = load_products_from_any_source(
            preferred_file='superdrug_batch_4.json',
            limit=20
        )
        
        # Use async processing for all products (single or batch)
        print(f"\nSTARTING ASYNC PROCESSING FOR {len(products_to_categorize)} PRODUCT(S)")
        print("=" * 60)
        print("Using ASYNC processing with explicit passthrough")
        
        stats = run_async_processing_with_search_tool(
            products_to_categorize, 
            max_concurrent=3,
            max_retries=2
        )
        
        # Save results to JSON
        save_results_to_json(stats['results'])
        
        # Log completion
        logger.log_event(
            LogLevel.INFO,
            "Async processing completed",
            category=ErrorCategory.CONFIGURATION,
            processing_mode="async",
            **stats
        )
        
        # Print final summary
        print(f"\n{'='*60}")
        print("PROCESSING COMPLETE")
        print(f"{'='*60}")
        print(f"Total: {stats['total']}")
        print(f"Successful: {stats['successful']} ({stats['successful']/max(stats['total'],1)*100:.1f}%)")
        print(f"Failed: {stats['failed']} ({stats['failed']/max(stats['total'],1)*100:.1f}%)")
        print(f"{'='*60}")
            
    except MappingError as e:
        logger.log_error(e, "Mapping system error", category=ErrorCategory.UNKNOWN)
        print(f"\n[ERROR] System error: {e}")
        exit(1)
    except Exception as e:
        logger.log_error(e, "Unexpected error", category=ErrorCategory.UNKNOWN)
        print(f"\n[ERROR] Unexpected error: {e}")
        exit(1)
    finally:
        logger.log_event(
            LogLevel.INFO,
            "Category mapping system shutting down",
            category=ErrorCategory.CONFIGURATION
        )


if __name__ == "__main__":
    main()
