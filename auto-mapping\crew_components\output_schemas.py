"""Pydantic output schemas for category mapping agent task results to enforce strict JSON outputs."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class CategoryMapping(BaseModel):
    """Schema for category mapping result from mapper agent."""
    level_1: str = Field("", description="Level 1 category name")
    level_2: str = Field("", description="Level 2 category name") 
    level_3: str = Field("", description="Level 3 category name")
    level_4: str = Field("", description="Level 4 category name")
    level_5: str = Field("", description="Level 5 category name")
    level_6: str = Field("", description="Level 6 category name")
    level_7: str = Field("", description="Level 7 category name")


class CostTracking(BaseModel):
    """Schema for tracking AI model costs during processing."""
    total_cost: float = Field(0.0, description="Total cost in USD for all operations")
    total_tokens: int = Field(0, description="Total tokens used across all operations")
    mapper_cost: float = Field(0.0, description="Cost for mapper agent operations")
    mapper_tokens: int = Field(0, description="Tokens used by mapper agent")
    validation_cost: float = Field(0.0, description="Cost for validation agent operations")
    validation_tokens: int = Field(0, description="Tokens used by validation agent")
    semantic_search_cost: float = Field(0.0, description="Cost for semantic search operations")
    semantic_search_tokens: int = Field(0, description="Tokens used for semantic search")
    retry_cost: float = Field(0.0, description="Additional cost from retry attempts")
    retry_count: int = Field(0, description="Number of retry attempts made")


class ValidationResult(BaseModel):
    """Schema for final category mapping result from validation agent."""
    level_1: str = Field("", description="Level 1 category name")
    level_2: str = Field("", description="Level 2 category name") 
    level_3: str = Field("", description="Level 3 category name")
    level_4: str = Field("", description="Level 4 category name")
    level_5: str = Field("", description="Level 5 category name")
    level_6: str = Field("", description="Level 6 category name")
    level_7: str = Field("", description="Level 7 category name")
    confidence_score: float = Field(..., description="Confidence score between 0.0 and 1.0")


class ProcessingResult(BaseModel):
    """Enhanced schema that includes cost tracking and processing metadata."""
    product_id: int = Field(..., description="Product ID")
    status: str = Field("success", description="Processing status: success, failed, partial")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Processing timestamp")
    processing_time: float = Field(0.0, description="Total processing time in seconds")
    
    # Category results
    final_result: Optional[ValidationResult] = Field(None, description="Final validated category mapping")
    mapper_result: Optional[CategoryMapping] = Field(None, description="Initial mapper result")
    
    # Cost tracking
    cost_tracking: CostTracking = Field(default_factory=CostTracking, description="Detailed cost information")
    
    # Processing metadata
    search_queries_used: List[str] = Field(default_factory=list, description="Semantic search queries executed")
    categories_found: int = Field(0, description="Number of matching categories found")
    retry_attempts: int = Field(0, description="Number of retry attempts made")
    error_message: Optional[str] = Field(None, description="Error message if processing failed")
