#!/usr/bin/env python
"""
Async Processing Module for Category Mapping

This module provides asynchronous processing capabilities using CrewAI's kickoff_async()
for improved performance when processing multiple products.

Key features:
- Explicit passthrough of mapper output to validation tasks
- Retry logic with exponential backoff and intelligent feedback
- Concurrent processing with configurable limits
- Comprehensive error handling and logging
"""

import asyncio
import json
import time
from typing import Dict, List, Tuple
from crewai import Crew, Process
from dotenv import load_dotenv

from crew_components.agents import mapper_agent, validation_agent
from crew_components.tasks import (
    create_mapping_task, 
    create_standalone_validation_task
)
from crew_components.error_handlers import (
    ProtectedExecution,
    wrap_agent_execution,
    wrap_tool_call,
    MappingError
)
from crew_components.logging_config import get_logger, ErrorCategory, LogLevel
from crew_components.cost_tracker import CostTracker, estimate_processing_cost
from crew_components.output_schemas import ProcessingResult, CostTracking
from crew_components import CategorySemanticSearchTool

# No database import needed - using semantic search for validation

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger()


def validate_category_with_embeddings(category_result: Dict, search_tool) -> Tuple[bool, str]:
    """
    Validate that each provided category level exists using semantic search.

    - For each level_1..level_7:
      - If value is missing and deeper levels are provided, record an error for the gap
      - If value is non-empty, search and ensure at least one result path contains that exact segment
    - Return all issues together so the agent can correct them in one go
    """
    try:
        issues: List[str] = []

        # Normalize all levels
        levels: List[Tuple[int, str]] = []
        for i in range(1, 8):
            raw_val = category_result.get(f'level_{i}', '')
            if isinstance(raw_val, str):
                val = raw_val.strip()
            else:
                val = '' if raw_val is None else str(raw_val).strip()
            levels.append((i, val))

        # Ensure at least level_1 is provided
        if not levels[0][1]:
            issues.append("Category level_1 is empty")

        # Detect gaps: empty level preceding a non-empty deeper level
        for i in range(0, 7):
            current_empty = (levels[i][1] == '')
            deeper_has_value = any(lv for _, lv in levels[i+1:])
            if current_empty and deeper_has_value:
                issues.append(f"Category level_{i+1} is empty while deeper levels are provided")

        # For each non-empty level, verify an exact segment exists somewhere in results
        for idx, name in levels:
            if not name:
                continue  # skip empty levels (already checked for gaps above)

            search_results = search_tool.search_tool.search(name, k=10)
            found_exact_segment = False
            for r in search_results:
                result_path = r.category.get('full_path', r.category.get('path', ''))
                if not isinstance(result_path, str) or not result_path:
                    continue
                segments = [seg.strip() for seg in result_path.split(" > ")]
                if any(seg.lower() == name.lower() for seg in segments):
                    found_exact_segment = True
                    break

            if not found_exact_segment:
                issues.append(f"Category level_{idx} does not exist: '{name}'")

        if issues:
            error_msg = "; ".join(issues)
            logger.log_event(
                LogLevel.WARNING,
                "Category validation failed - issues found",
                category=ErrorCategory.VALIDATION,
                error=error_msg,
                levels={f"level_{i}": n for i, n in levels}
            )
            return False, error_msg

        # All checks passed
        logger.log_event(
            LogLevel.INFO,
            "All provided category levels exist (validation passed)",
            category=ErrorCategory.VALIDATION,
            levels={f"level_{i}": n for i, n in levels if n}
        )
        return True, ""

    except Exception as e:
        # Embeddings search error: do not block mapping, return soft-pass with reason
        error_msg = f"Embeddings validation error: {str(e)}"
        logger.log_error(
            e,
            "Failed to validate category using embeddings",
            category=ErrorCategory.TOOL
        )
        return True, f"Embeddings validation skipped due to error: {str(e)}"


def initialize_search_tool():
    """Initialize and configure the semantic search tool with error handling."""
    print("=" * 60)
    print("INITIALIZING CATEGORY MAPPING SYSTEM")
    print("=" * 60)
    
    with ProtectedExecution(
        "search_tool_initialization",
        category=ErrorCategory.TOOL,
        raise_on_error=True
    ) as protected:
        try:
            # Create semantic search tool
            search_tool = CategorySemanticSearchTool()
            
            # Wrap the tool with error handling
            search_tool = wrap_tool_call(search_tool)
            
            # Log successful initialization
            logger.log_event(
                LogLevel.INFO,
                "Search tool initialized successfully",
                category=ErrorCategory.CONFIGURATION
            )
            
            # Check capabilities
            if search_tool.search_tool.semantic_available:
                print("[OK] Semantic search ready:")
                stats = search_tool.search_tool.embeddings_manager.get_statistics()
                print(f"     - Semantic search: {stats['num_embeddings']} embeddings")
                print(f"     - Storage: {stats['storage_size_mb']} MB")
                
                logger.log_event(
                    LogLevel.INFO,
                    "Semantic search enabled",
                    category=ErrorCategory.CONFIGURATION,
                    num_embeddings=stats['num_embeddings']
                )
            else:
                # Fail fast if embeddings are not available
                msg = (
                    "Embeddings not available. Set EMBEDDINGS_DIR in auto-mapping/.env "
                    "to the absolute path of your embeddings directory or run "
                    "'python auto-mapping/generate_embeddings.py' to generate them."
                )
                logger.log_error(
                    Exception(msg),
                    "Semantic search unavailable - aborting initialization",
                    category=ErrorCategory.CONFIGURATION
                )
                print(f"[ERROR] {msg}")
                raise MappingError(msg)
            
            protected.set_result(search_tool)
            return search_tool
            
        except Exception as e:
            logger.log_error(
                e,
                "Failed to initialize search tool",
                category=ErrorCategory.TOOL
            )
            print(f"[ERROR] Failed to initialize search tool: {e}")
            print("\nTroubleshooting:")
            print("1. Ensure embeddings files exist. Run 'python auto-mapping/generate_embeddings.py' if needed.")
            print("2. Check .env file for EMBEDDINGS_DIR and OPENAI_API_KEY.")
            raise MappingError("Search tool initialization failed") from e


def get_validation_data_from_output(crew_result) -> Dict:
    """Extract validation data from crew result - works with both sync and async results."""
    try:
        # Try pydantic first
        if hasattr(crew_result, 'pydantic') and getattr(crew_result, 'pydantic') is not None:
            return crew_result.pydantic.model_dump()
        
        # Try raw JSON string
        if hasattr(crew_result, 'raw') and isinstance(crew_result.raw, str):
            if crew_result.raw.strip().startswith('{'):
                try:
                    return json.loads(crew_result.raw)
                except json.JSONDecodeError:
                    pass
        
        # Try direct dict
        if isinstance(crew_result, dict):
            return crew_result
        
        # Try tasks_output (get last task = validation task)
        if hasattr(crew_result, 'tasks_output') and crew_result.tasks_output:
            validation_output = crew_result.tasks_output[-1]
            if hasattr(validation_output, 'pydantic') and getattr(validation_output, 'pydantic') is not None:
                return validation_output.pydantic.model_dump()
            elif hasattr(validation_output, 'raw') and isinstance(validation_output.raw, str):
                if validation_output.raw.strip().startswith('{'):
                    try:
                        return json.loads(validation_output.raw)
                    except json.JSONDecodeError:
                        pass
        
        return {}
    except Exception:
        return {}

 

def optimize_product_data_for_tokens(product_data: Dict) -> Dict:
    """
    Optimize product data to minimize token usage by removing description
    when breadcrumbs are available.

    Args:
        product_data: Original product data dictionary

    Returns:
        Optimized product data dictionary
    """
    optimized_data = product_data.copy()

    # If breadcrumbs are available, remove description to save tokens
    if product_data.get('breadcrumbs') and len(product_data.get('breadcrumbs', [])) > 0:
        optimized_data.pop('product_description', None) 
   
    return optimized_data



async def process_products_explicit_passthrough(products: List[Dict], 
                                              search_tool, 
                                              max_concurrent: int = 3,
                                              max_retries: int = 2) -> Dict:
    """
    Process products with explicit passthrough of mapper output to validation task.
    Each product gets its own product_info input and validation gets explicit mapper_output.
    
    Args:
        products: List of products to process
        search_tool: Initialized category semantic search tool  
        max_concurrent: Maximum concurrent operations
        max_retries: Maximum retry attempts per product
        
    Returns:
        Processing statistics and results
    """
    print(f"\\n{'='*60}")
    print(f"EXPLICIT PASSTHROUGH ASYNC PATTERN")
    print(f"{'='*60}")
    print(f"Products: {len(products)}")
    print(f"Max concurrent: {max_concurrent}")
    print(f"Max retries: {max_retries}")
    print(f"Mode: Explicit mapper output -> validation input with retry logic")
    print(f"Embeddings validation: Enabled (using semantic search)")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    # Create wrapped agents
    wrapped_mapper = wrap_agent_execution(mapper_agent)
    wrapped_validator = wrap_agent_execution(validation_agent)
    wrapped_mapper.tools = [search_tool]
    wrapped_validator.tools = [search_tool]
    
    # Create individual tasks for each product (not crew-based)
    mapping_tasks = [create_mapping_task(wrapped_mapper) for _ in products]
    validation_tasks = [create_standalone_validation_task(wrapped_validator) for _ in products]
    
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_product_explicit(product, map_task, val_task, max_retries=2):
        async with semaphore:
            product_id = product.get('product_id', 'unknown')
            print(f"[Explicit] Processing {product_id}")
            
            # Initialize cost tracking
            cost_tracker = CostTracker()
            start_time = time.time()
            search_queries = []
            
            # Set thread-local variables for semantic search tracking
            import threading
            current_thread = threading.current_thread()
            current_thread.cost_tracker = cost_tracker
            current_thread.search_queries = search_queries
            
            retry_feedback = ""  # Initialize feedback for retries
            last_error = None
            
            for attempt in range(max_retries):
                try:
                    # Step 1: Run mapping task
                    map_crew = Crew(
                        agents=[wrapped_mapper],
                        tasks=[map_task], 
                        memory=True if attempt > 0 else False, # Use memory on retries. Agent learns from previous attempts.
                        process=Process.sequential,
                        verbose=False
                    )
                    
                    map_inputs = {
                        'product_info': json.dumps(optimize_product_data_for_tokens(product)),
                        'retry_feedback': retry_feedback
                    }
                    
                    map_result = await map_crew.kickoff_async(inputs=map_inputs)
                    mapper_output = extract_mapper_output(map_result)
                    
                    # Track mapper costs
                    input_text = json.dumps(map_inputs)
                    output_text = json.dumps(mapper_output) if mapper_output else ""
                    cost_tracker.track_mapper_operation(input_text, output_text)
                    
                    # Step 2: Run validation task with explicit mapper output
                    val_crew = Crew(
                        agents=[wrapped_validator],
                        tasks=[val_task],
                        memory=True if attempt > 0 else False,  # Use memory on retries. Agent learns from previous attempts.
                        process=Process.sequential,
                        verbose=False
                    )
                    
                    val_inputs = {
                        'product_info': json.dumps(optimize_product_data_for_tokens(product)),
                        'mapper_output': json.dumps(mapper_output),
                        'retry_feedback': retry_feedback  # Always provide retry_feedback (empty string on first attempt)
                    }
                    
                    val_result = await val_crew.kickoff_async(inputs=val_inputs)
                    result = process_crew_result(product, val_result)
                    
                    # Track validation costs
                    val_input_text = json.dumps(val_inputs)
                    val_output_text = json.dumps(result.get('result', {}))
                    cost_tracker.track_validation_operation(val_input_text, val_output_text)
                    
                    # Check if the mapping is valid (same validation as auto_mapping.py)
                    if result.get('status') == 'success' and result.get('result', {}).get('level_1'):
                        # Additional embeddings validation check
                        category_data = result.get('result', {})
                        is_valid_in_embeddings, validation_error = validate_category_with_embeddings(category_data, search_tool)
                        
                        if is_valid_in_embeddings:
                            print(f"[Explicit] [OK] {product_id} succeeded on attempt {attempt + 1} (embeddings validated)")
                            
                            # Finalize cost tracking and add to result
                            processing_time = time.time() - start_time
                            final_costs = cost_tracker.finalize_costs()
                            
                            # Enhance result with cost tracking
                            result['cost_tracking'] = final_costs.dict()
                            result['processing_time'] = processing_time
                            result['retry_attempts'] = attempt
                            result['search_queries_used'] = search_queries
                            result['embeddings_validated'] = True
                            
                            return result
                        else:
                            # Embeddings validation failed, treat as mapping error for retry
                            error_msg = f"Category validation failed: {validation_error}"
                            print(f"[Explicit] [EMBEDDINGS_VALIDATION_FAILED] {product_id} on attempt {attempt + 1}: {error_msg}")
                            raise MappingError(error_msg)
                    else:
                        # Invalid result, treat as mapping error
                        raise MappingError(f"Invalid category mapping returned: {result.get('result', 'None')}")
                        
                except Exception as e:
                    print(f"[Explicit] [WARNING] {product_id} attempt {attempt + 1} failed: {str(e)[:50]}")
                    last_error = e
                    
                    # Track retry cost (estimate additional cost for retry)
                    cost_tracker.track_retry_attempt()
                    
                    # Prepare feedback for the NEXT attempt (same logic as auto_mapping.py)
                    if isinstance(e, MappingError):
                        error_str = str(e)
                        if "Category validation failed" in error_str:
                            feedback_message = (
                                "\\n\\n--- PREVIOUS ATTEMPT FEEDBACK ---\\n"
                                f"On your last attempt, you provided a category that does not exist in our database. The error was: '{e}'.\\n"
                                "You MUST select categories that actually exist in our database or remove the level if it does not exist.\\n"
                                "Pay attention to the exact category names returned by the search tool and use them precisely.\\n"
                                "\\n----------------------------------\\n"
                            )
                        elif "Invalid category mapping returned:" in error_str:
                            feedback_message = (
                                "\\n\\n--- PREVIOUS ATTEMPT FEEDBACK ---\\n"
                                f"INVALID RESULT: {e}\\n"
                                "\\n"
                                "CRITICAL: Use EXACT TEXT from search results!\\n"
                                "- Search for categories using the tool\\n"
                                "- Copy exact category names (do NOT modify/paraphrase)\\n"
                                "- Split path correctly: 'A > B > C' becomes level_1='A', level_2='B', level_3='C'\\n"
                                "\\n"
                                "You MUST return valid categories that exist in our system!"
                                "\\n----------------------------------\\n"
                            )
                        elif "INVALID CATEGORY PATH:" in error_str:
                            # Extract specific category information from the detailed error
                            feedback_message = (
                                "\\n\\n--- PREVIOUS ATTEMPT FEEDBACK ---\\n"
                                f"CATEGORY VALIDATION FAILED: {e}\\n"
                                "\\n"
                                "SPECIFIC ISSUE DETECTED: Your category path contains invalid subcategories.\\n"
                                "CRITICAL: Use the search tool to find VALID categories that actually exist!\\n"
                                "- Do NOT create or invent category names\\n"
                                "- Search for the product type and use EXACT matches from results\\n"
                                "- If suggested replacement is provided above, consider using it or search for similar valid options\\n"
                                "\\n"
                                "Fix the flagged category and try again with valid, existing categories only!"
                                "\\n----------------------------------\\n"
                            )
                        else:
                            feedback_message = (
                                "\\n\\n--- PREVIOUS ATTEMPT FEEDBACK ---\\n"
                                f"On your last attempt, you produced an invalid result. The error was: '{e}'.\\n"
                                "This is not acceptable. You MUST provide a valid category path with at least 'level_1' populated.\\n"
                                "Review the product information and your search strategy. Ensure you are selecting a valid category from the search tool results.\\n"
                                "Do NOT return an empty or malformed result again."
                                "\\n----------------------------------\\n"
                            )
                    else:
                        feedback_message = (
                            "\\n\\n--- PREVIOUS ATTEMPT FEEDBACK ---\\n"
                            f"Your previous attempt failed with an error: {str(e)}.\\n"
                            "This might have been a tool usage error or a formatting problem.\\n"
                            "Please review the instructions carefully and try again. Pay close attention to the tool inputs and the required final JSON format."
                            "\\n----------------------------------\\n"
                        )
                    retry_feedback = feedback_message
                    
                    if attempt < max_retries - 1:
                        # Wait before retry (exponential backoff)
                        wait_time = 2 ** attempt
                        print(f"[Explicit] Waiting {wait_time}s before retry...")
                        await asyncio.sleep(wait_time)
            
            # All retries failed
            print(f"[Explicit] [FAILED] {product_id} failed after {max_retries} attempts")
            logger.log_error(
                last_error,
                f"Product mapping failed after {max_retries} attempts",
                category=ErrorCategory.AGENT,
                product_id=product_id
            )
            
            # Finalize cost tracking for failed result
            processing_time = time.time() - start_time
            final_costs = cost_tracker.finalize_costs()
            
            failed_result = create_product_result_dict(
                product, 
                'failed', 
                error=f'Mapping failed after {max_retries} attempts: {str(last_error)[:100]}'
            )
            
            # Add cost tracking to failed result
            failed_result['cost_tracking'] = final_costs.dict()
            failed_result['processing_time'] = processing_time
            failed_result['retry_attempts'] = max_retries
            failed_result['search_queries_used'] = search_queries
            
            return failed_result
    
    # Create tasks for all products
    async_tasks = []
    for i, product in enumerate(products):
        task = process_product_explicit(product, mapping_tasks[i], validation_tasks[i], max_retries)
        async_tasks.append(task)
    
    # Execute all tasks concurrently
    results = await asyncio.gather(*async_tasks, return_exceptions=True)
    
    # Process results and aggregate costs
    processed_results = []
    total_costs = CostTracking()
    total_processing_time = 0.0
    total_retries = 0
    all_search_queries = []
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append(create_product_result_dict(products[i], 'error', error=str(result)))
        else:
            processed_results.append(result)
            
            # Aggregate cost data
            if 'cost_tracking' in result:
                cost_data = result['cost_tracking']
                total_costs.total_cost += cost_data.get('total_cost', 0)
                total_costs.total_tokens += cost_data.get('total_tokens', 0)
                total_costs.mapper_cost += cost_data.get('mapper_cost', 0)
                total_costs.mapper_tokens += cost_data.get('mapper_tokens', 0)
                total_costs.validation_cost += cost_data.get('validation_cost', 0)
                total_costs.validation_tokens += cost_data.get('validation_tokens', 0)
                total_costs.semantic_search_cost += cost_data.get('semantic_search_cost', 0)
                total_costs.semantic_search_tokens += cost_data.get('semantic_search_tokens', 0)
                total_costs.retry_cost += cost_data.get('retry_cost', 0)
                total_costs.retry_count += cost_data.get('retry_count', 0)
                
            if 'processing_time' in result:
                total_processing_time += result['processing_time']
            if 'retry_attempts' in result:
                total_retries += result['retry_attempts']
            if 'search_queries_used' in result:
                all_search_queries.extend(result['search_queries_used'])
    
    # Calculate stats
    successful = sum(1 for r in processed_results if r.get('status') == 'success')
    duration = time.time() - start_time
    
    # Count embeddings validation successes
    embeddings_validated = sum(1 for r in processed_results if r.get('embeddings_validated', False))
    
    print(f"\\n{'='*60}")
    print(f"EXPLICIT PASSTHROUGH COMPLETE")
    print(f"{'='*60}")
    print(f"Total: {len(products)}")
    print(f"Successful: {successful}")
    print(f"Embeddings Validated: {embeddings_validated}")
    print(f"Failed: {len(products) - successful}")
    print(f"Duration: {duration:.1f}s")
    print(f"Total Cost: ${total_costs.total_cost:.4f}")
    print(f"Total Tokens: {total_costs.total_tokens:,}")
    print(f"Search Queries: {len(all_search_queries)}")
    print(f"Retries: {total_retries}")
    print(f"{'='*60}")
    
    return {
        'total': len(products),
        'successful': successful, 
        'failed': len(products) - successful,
        'embeddings_validated': embeddings_validated,
        'duration': duration,
        'results': processed_results,
        'cost_summary': {
            'total_cost': total_costs.total_cost,
            'total_tokens': total_costs.total_tokens,
            'mapper_cost': total_costs.mapper_cost,
            'validation_cost': total_costs.validation_cost,
            'semantic_search_cost': total_costs.semantic_search_cost,
            'retry_cost': total_costs.retry_cost,
            'cost_per_product': total_costs.total_cost / len(products) if len(products) > 0 else 0,
            'search_queries_count': len(all_search_queries),
            'total_retries': total_retries,
            'avg_processing_time': total_processing_time / len(products) if len(products) > 0 else 0
        }
    }


def extract_mapper_output(crew_result) -> Dict:
    """Extract mapper output from crew result for explicit passthrough."""
    try:
        # Try to get the result from the mapping task
        if hasattr(crew_result, 'pydantic') and getattr(crew_result, 'pydantic') is not None:
            return crew_result.pydantic.model_dump()
        elif hasattr(crew_result, 'raw') and isinstance(crew_result.raw, str):
            if crew_result.raw.strip().startswith('{'):
                try:
                    return json.loads(crew_result.raw)
                except json.JSONDecodeError:
                    pass
        elif hasattr(crew_result, 'tasks_output') and crew_result.tasks_output:
            # Get the first task output (mapping task)
            mapping_output = crew_result.tasks_output[0]
            if hasattr(mapping_output, 'pydantic') and getattr(mapping_output, 'pydantic') is not None:
                return mapping_output.pydantic.model_dump()
            elif hasattr(mapping_output, 'raw') and isinstance(mapping_output.raw, str):
                if mapping_output.raw.strip().startswith('{'):
                    try:
                        return json.loads(mapping_output.raw)
                    except json.JSONDecodeError:
                        pass
        
        # Return empty mapping if extraction fails
        return {
            'level_1': '',
            'level_2': '',
            'level_3': '',
            'level_4': '',
            'level_5': '',
            'level_6': '',
            'level_7': ''
        }
    except Exception:
        return {
            'level_1': '',
            'level_2': '',
            'level_3': '',
            'level_4': '',
            'level_5': '',
            'level_6': '',
            'level_7': ''
        }


def create_product_result_dict(product: Dict, status: str, result=None, error=None) -> Dict:
    """Create standardized product result dictionary."""
    result_dict = {
        'product_id': product.get('product_id', 'unknown'),
        'product_name': product.get('product_name', ''),
        'product_description': product.get('product_description', ''),
        'product_url': product.get('url', ''),
        'breadcrumbs': product.get('breadcrumbs', []),
        'status': status
    }
    
    if result:
        result_dict['result'] = result
    if error:
        result_dict['error'] = str(error)[:200]
    
    return result_dict


def process_crew_result(product: Dict, crew_result) -> Dict:
    """Process crew result into standardized format."""
    try:
        validation_data = get_validation_data_from_output(crew_result)
        
        if isinstance(validation_data, dict) and validation_data.get('level_1'):
            category_path = {
                'level_1': validation_data.get('level_1', ''),
                'level_2': validation_data.get('level_2', ''),
                'level_3': validation_data.get('level_3', ''),
                'level_4': validation_data.get('level_4', ''),
                'level_5': validation_data.get('level_5', ''),
                'level_6': validation_data.get('level_6', ''),
                'level_7': validation_data.get('level_7', '')
            }
            
            result_with_confidence = {**category_path, 'confidence_score': validation_data.get('confidence_score', 0.0)}
            return create_product_result_dict(product, 'success', result=result_with_confidence)
        else:
            raise ValueError("Invalid category mapping result")
            
    except Exception as e:
        return create_product_result_dict(product, 'failed', error=str(e)[:100])





def run_async_processing_with_search_tool(products: List[Dict], max_concurrent: int = 3, max_retries: int = 2) -> Dict:
    """
    Complete async processing pipeline: initialize search tool and process products.
    
    Args:
        products: List of products to process
        max_concurrent: Maximum concurrent operations
        max_retries: Maximum retry attempts per product
        
    Returns:
        Processing statistics
    """
    # Initialize search tool
    search_tool = initialize_search_tool()
    
    # Run async processing
    return run_async_processing(products, search_tool, max_concurrent, max_retries)


def run_async_processing(products: List[Dict], search_tool, max_concurrent: int = 3, max_retries: int = 2) -> Dict:
    """
    Run async processing from synchronous code.
    
    Args:
        products: List of products to process
        search_tool: Initialized category semantic search tool
        max_concurrent: Maximum concurrent operations
        max_retries: Maximum retry attempts per product
        
    Returns:
        Processing statistics
    """
    # Handle different event loop scenarios
    try:
        # Try to get existing event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is already running, we need to use a different approach
            import concurrent.futures
            import threading
            
            def run_in_thread():
                # Create new event loop in thread
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        process_products_explicit_passthrough(products, search_tool, max_concurrent, max_retries)
                    )
                finally:
                    new_loop.close()
            
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result()
        else:
            # Loop exists but not running, use it
            return loop.run_until_complete(
                process_products_explicit_passthrough(products, search_tool, max_concurrent, max_retries)
            )
    except RuntimeError:
        # No event loop, create one
        return asyncio.run(process_products_explicit_passthrough(products, search_tool, max_concurrent, max_retries))