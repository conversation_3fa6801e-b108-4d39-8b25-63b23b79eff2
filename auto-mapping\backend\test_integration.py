#!/usr/bin/env python3
"""
Test script to validate CrewAI async_processor integration with backend routes.

This script tests:
1. Import of async_processor from parent directory
2. Basic functionality of convert_crewai_result_to_response
3. Mock API payload structure
"""

import sys
import os
import json

# Add the parent directory to sys.path to import async_processor
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_imports():
    """Test if we can import required modules."""
    print("Testing imports...")
    
    try:
        from async_processor import run_async_processing_with_search_tool
        print("[OK] Successfully imported async_processor")
    except ImportError as e:
        print(f"[ERROR] Failed to import async_processor: {e}")
        return False
    
    try:
        from pydantic import BaseModel
        from fastapi import FastAPI
        print("[OK] Successfully imported FastAPI dependencies")
    except ImportError as e:
        print(f"[ERROR] Failed to import FastAPI dependencies: {e}")
        return False
    
    return True

def test_conversion_function():
    """Test the CrewAI result to API response conversion."""
    print("\nTesting result conversion...")
    
    # Import the conversion function from our new routes
    sys.path.append(os.path.dirname(__file__))
    from routes import Product, CategoryResponse, convert_crewai_result_to_response
    
    # Create a mock product
    mock_product = Product(
        product_id=12345,
        title="Organic Tomato Seeds",
        description="Premium organic tomato seeds for home gardening"
    )
    
    # Mock successful CrewAI result
    mock_success_result = {
        'status': 'success',
        'final_result': {
            'level_1': 'Garden & DIY',
            'level_2': 'Garden Plants, Seeds & Bulbs', 
            'level_3': 'Seeds',
            'level_4': 'Vegetable Seeds',
            'level_5': '',
            'level_6': '',
            'level_7': '',
            'confidence_score': 0.95
        }
    }
    
    # Test successful conversion
    result = convert_crewai_result_to_response(mock_product, mock_success_result)
    print(f"[OK] Successful result conversion:")
    print(f"  Product ID: {result.product_id}")
    print(f"  Level 1: {result.level_1}")
    print(f"  Level 2: {result.level_2}")
    print(f"  Level 3: {result.level_3}")
    print(f"  Confidence: {result.confidence_score}")
    
    # Mock failed result
    mock_failed_result = {'status': 'failed', 'error': 'Processing error'}
    result_failed = convert_crewai_result_to_response(mock_product, mock_failed_result)
    print(f"[OK] Failed result conversion:")
    print(f"  Product ID: {result_failed.product_id}")
    print(f"  Level 1: {result_failed.level_1}")
    print(f"  Confidence: {result_failed.confidence_score}")
    
    return True

def test_api_payload_structure():
    """Test the expected API request/response structure."""
    print("\nTesting API payload structure...")
    
    # Expected input format for the API
    api_request = {
        "products": [
            {
                "product_id": 12345,
                "title": "Organic Tomato Seeds",
                "description": "Premium organic seeds for home gardening"
            },
            {
                "product_id": 67890,
                "title": "Garden Spade Tool", 
                "description": "Heavy-duty gardening spade for digging"
            }
        ]
    }
    
    print("[OK] Sample API request structure:")
    print(json.dumps(api_request, indent=2))
    
    # Expected response format
    api_response = {
        "status": "Success",
        "message": "Successfully mapped categories using CrewAI.",
        "data": [
            {
                "product_id": 12345,
                "level_1": "Garden & DIY",
                "level_2": "Garden Plants, Seeds & Bulbs",
                "level_3": "Seeds",
                "level_4": "Vegetable Seeds",
                "level_5": "",
                "level_6": "",
                "level_7": "",
                "confidence_score": 0.95
            },
            {
                "product_id": 67890,
                "level_1": "Garden & DIY",
                "level_2": "Garden Tools & Equipment",
                "level_3": "Hand Tools",
                "level_4": "Spades",
                "level_5": "",
                "level_6": "",
                "level_7": "",
                "confidence_score": 0.88
            }
        ]
    }
    
    print("\n[OK] Expected API response structure:")
    print(json.dumps(api_response, indent=2))
    
    return True

def test_environment():
    """Test environment setup for CrewAI processing."""
    print("\nTesting environment setup...")
    
    # Check for required environment variables
    required_env_vars = [
        'OPENAI_API_KEY',
        'POSTGRES_HOST',
        'POSTGRES_DB',
        'POSTGRES_USER', 
        'POSTGRES_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"[WARNING] Missing environment variables: {missing_vars}")
        print("  Make sure .env file is properly configured")
    else:
        print("[OK] All required environment variables are set")
    
    # Check for embeddings directory
    embeddings_dir = os.path.join(os.path.dirname(__file__), '..', 'embeddings')
    if os.path.exists(embeddings_dir):
        print("[OK] Embeddings directory found")
        
        # Check for key embedding files
        embedding_files = ['category_embeddings.npz', 'category_metadata.json', 'embeddings.faiss']
        for file in embedding_files:
            file_path = os.path.join(embeddings_dir, file)
            if os.path.exists(file_path):
                print(f"[OK] Found {file}")
            else:
                print(f"[WARNING] Missing {file}")
    else:
        print("[WARNING] Embeddings directory not found")
        print("  Run 'python generate_embeddings.py' to create embeddings")
    
    return True

def main():
    """Run all integration tests."""
    print("=" * 60)
    print("CrewAI Backend Integration Test")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Conversion Function", test_conversion_function), 
        ("API Payload Structure", test_api_payload_structure),
        ("Environment Setup", test_environment)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"[ERROR] {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "[PASS]" if success else "[FAIL]"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n[SUCCESS] All tests passed! Backend integration is ready.")
    else:
        print("\n[WARNING] Some tests failed. Check the issues above.")
        print("   The backend may still work but with limited functionality.")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()