"""
Cost Tracking Utility for CrewAI Category Mapping

This module provides utilities to track costs for AI model usage
including OpenAI API calls, CrewAI operations, and semantic search.
"""

import re
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from .output_schemas import CostTracking
import tiktoken


class CostTracker:
    """Tracks costs for AI operations during category mapping."""
    
    # OpenAI pricing (as of 2024/2025 - update as needed)
    MODEL_COSTS = {
        'gpt-4o-mini': {
            'input': 0.000150,   # per 1K tokens
            'output': 0.000600   # per 1K tokens
        },
        'gpt-4o': {
            'input': 0.005,      # per 1K tokens  
            'output': 0.015      # per 1K tokens
        },
        'gpt-3.5-turbo': {
            'input': 0.0005,     # per 1K tokens
            'output': 0.0015     # per 1K tokens
        },
        'text-embedding-3-small': {
            'input': 0.00002,    # per 1K tokens
            'output': 0.0        # no output cost for embeddings
        },
        'text-embedding-3-large': {
            'input': 0.00013,    # per 1K tokens
            'output': 0.0        # no output cost for embeddings
        }
    }
    
    def __init__(self):
        """Initialize cost tracker."""
        self.costs = CostTracking()
        self.operations = []  # List of operation details for debugging
        self.encoding = tiktoken.get_encoding("cl100k_base")
        
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        if not text:
            return 0
        try:
            return len(self.encoding.encode(str(text)))
        except Exception:
            # Fallback: rough estimate
            return len(str(text).split()) * 1.3
    
    def calculate_cost(self, model: str, input_tokens: int, output_tokens: int = 0) -> float:
        """Calculate cost for a model operation."""
        if model not in self.MODEL_COSTS:
            print(f"Warning: Unknown model '{model}', cost tracking may be inaccurate")
            return 0.0
        
        pricing = self.MODEL_COSTS[model]
        input_cost = (input_tokens / 1000.0) * pricing['input']
        output_cost = (output_tokens / 1000.0) * pricing['output']
        
        return input_cost + output_cost
    
    def extract_cost_from_crewai_result(self, result: Any) -> Dict[str, float]:
        """Extract cost information from CrewAI task result."""
        costs = {'cost': 0.0, 'tokens': 0}
        
        # Try to extract from result object
        if hasattr(result, 'usage_metrics'):
            # Future-proofing for if CrewAI adds usage metrics
            metrics = result.usage_metrics
            costs['cost'] = metrics.get('total_cost', 0.0)
            costs['tokens'] = metrics.get('total_tokens', 0)
        elif hasattr(result, 'raw') and isinstance(result.raw, str):
            # Try to extract from raw output if it contains cost info
            raw = result.raw
            costs.update(self._parse_cost_from_text(raw))
        
        return costs
    
    def _parse_cost_from_text(self, text: str) -> Dict[str, float]:
        """Parse cost information from text output."""
        costs = {'cost': 0.0, 'tokens': 0}
        
        # Look for common cost patterns in text
        cost_patterns = [
            r'cost[:\s]*\$?([0-9.]+)',
            r'price[:\s]*\$?([0-9.]+)',  
            r'([0-9.]+)\s*USD',
            r'([0-9.]+)\s*dollars?'
        ]
        
        token_patterns = [
            r'([0-9,]+)\s*tokens?',
            r'token[s\s]*[:\s]*([0-9,]+)',
            r'usage[:\s]*([0-9,]+)'
        ]
        
        for pattern in cost_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    costs['cost'] = float(match.group(1))
                    break
                except (ValueError, IndexError):
                    continue
        
        for pattern in token_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    token_str = match.group(1).replace(',', '')
                    costs['tokens'] = int(token_str)
                    break
                except (ValueError, IndexError):
                    continue
                    
        return costs
    
    def track_mapper_operation(self, input_text: str, output_text: str, model: str = "gpt-4o-mini"):
        """Track cost for mapper agent operation."""
        input_tokens = self.count_tokens(input_text)
        output_tokens = self.count_tokens(output_text)
        cost = self.calculate_cost(model, input_tokens, output_tokens)
        
        self.costs.mapper_cost += cost
        self.costs.mapper_tokens += input_tokens + output_tokens
        
        self.operations.append({
            'type': 'mapper',
            'model': model,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'cost': cost,
            'timestamp': datetime.now().isoformat()
        })
    
    def track_validation_operation(self, input_text: str, output_text: str, model: str = "gpt-4o-mini"):
        """Track cost for validation agent operation."""
        input_tokens = self.count_tokens(input_text)
        output_tokens = self.count_tokens(output_text)
        cost = self.calculate_cost(model, input_tokens, output_tokens)
        
        self.costs.validation_cost += cost
        self.costs.validation_tokens += input_tokens + output_tokens
        
        self.operations.append({
            'type': 'validation',
            'model': model,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'cost': cost,
            'timestamp': datetime.now().isoformat()
        })
    
    def track_semantic_search(self, query: str, model: str = "text-embedding-3-small"):
        """Track cost for semantic search embedding operation."""
        input_tokens = self.count_tokens(query)
        cost = self.calculate_cost(model, input_tokens, 0)
        
        self.costs.semantic_search_cost += cost
        self.costs.semantic_search_tokens += input_tokens
        
        self.operations.append({
            'type': 'semantic_search',
            'model': model,
            'input_tokens': input_tokens,
            'output_tokens': 0,
            'cost': cost,
            'query': query[:100] + '...' if len(query) > 100 else query,
            'timestamp': datetime.now().isoformat()
        })
    
    def track_retry_attempt(self, additional_cost: float = 0.0):
        """Track additional cost from retry attempts."""
        self.costs.retry_count += 1
        self.costs.retry_cost += additional_cost
        
        self.operations.append({
            'type': 'retry',
            'additional_cost': additional_cost,
            'timestamp': datetime.now().isoformat()
        })
    
    def finalize_costs(self) -> CostTracking:
        """Calculate final total costs and return tracking object."""
        self.costs.total_cost = (
            self.costs.mapper_cost + 
            self.costs.validation_cost + 
            self.costs.semantic_search_cost + 
            self.costs.retry_cost
        )
        
        self.costs.total_tokens = (
            self.costs.mapper_tokens +
            self.costs.validation_tokens + 
            self.costs.semantic_search_tokens
        )
        
        return self.costs
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """Get detailed cost summary for logging/debugging."""
        return {
            'costs': self.costs.dict(),
            'operations': self.operations,
            'cost_per_operation': len([op for op in self.operations if op.get('cost', 0) > 0]),
            'total_operations': len(self.operations)
        }
    
    def reset(self):
        """Reset cost tracker for new processing."""
        self.costs = CostTracking()
        self.operations = []


def estimate_processing_cost(product_count: int, avg_product_length: int = 500) -> Dict[str, float]:
    """
    Estimate processing costs for a batch of products.
    
    Args:
        product_count: Number of products to process
        avg_product_length: Average length of product descriptions
        
    Returns:
        Dictionary with cost estimates
    """
    # Rough estimates based on typical usage patterns
    avg_tokens_per_product = avg_product_length * 1.3  # rough tokens estimate
    
    # Mapper agent: product info + task prompt + search results
    mapper_tokens_per_product = avg_tokens_per_product + 800  # prompt overhead + search results
    mapper_output_tokens = 100  # category JSON output
    
    # Validation agent: product info + mapper result + task prompt
    validation_tokens_per_product = avg_tokens_per_product + 300  # less overhead
    validation_output_tokens = 120  # category JSON + confidence
    
    # Semantic search: typically 3-5 queries per product  
    search_queries_per_product = 4
    search_tokens_per_query = 20
    
    # Calculate costs using gpt-4o-mini pricing
    mapper_cost = CostTracker().calculate_cost(
        'gpt-4o-mini', 
        mapper_tokens_per_product * product_count,
        mapper_output_tokens * product_count
    )
    
    validation_cost = CostTracker().calculate_cost(
        'gpt-4o-mini',
        validation_tokens_per_product * product_count, 
        validation_output_tokens * product_count
    )
    
    search_cost = CostTracker().calculate_cost(
        'text-embedding-3-small',
        search_tokens_per_query * search_queries_per_product * product_count,
        0
    )
    
    # Add 20% buffer for retries and variations
    total_cost = (mapper_cost + validation_cost + search_cost) * 1.2
    
    return {
        'estimated_total_cost': round(total_cost, 4),
        'mapper_cost': round(mapper_cost, 4),
        'validation_cost': round(validation_cost, 4), 
        'search_cost': round(search_cost, 4),
        'cost_per_product': round(total_cost / product_count, 4),
        'estimated_tokens': int((mapper_tokens_per_product + validation_tokens_per_product) * product_count)
    }


if __name__ == "__main__":
    # Test cost tracking functionality
    tracker = CostTracker()
    
    # Simulate operations
    tracker.track_semantic_search("organic tomato seeds")
    tracker.track_mapper_operation("Product: Organic Seeds...", '{"level_1": "Garden"}')
    tracker.track_validation_operation("Product: Seeds... Mapper: Garden", '{"level_1": "Garden", "confidence": 0.9}')
    
    costs = tracker.finalize_costs()
    print(f"Total cost: ${costs.total_cost:.4f}")
    print(f"Total tokens: {costs.total_tokens}")
    
    # Test cost estimation
    estimate = estimate_processing_cost(100, 400)
    print(f"\nEstimated cost for 100 products: ${estimate['estimated_total_cost']}")
    print(f"Cost per product: ${estimate['cost_per_product']}")