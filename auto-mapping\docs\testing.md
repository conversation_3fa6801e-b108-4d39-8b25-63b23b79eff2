# Testing Guide

## 🧪 Test Suite Overview

The Category Mapping System includes a comprehensive test suite with **198 total tests** covering unit, integration, and end-to-end scenarios with **74% success rate** and **37.53% code coverage**.

## 📊 Test Statistics

### Test Coverage Summary
- **Total Tests:** 198 tests across 6 test files
- **Passing Tests:** 143/192 (74% success rate)
- **Skipped Tests:** 6 (database/performance tests)
- **Overall Coverage:** 37.53% (significant improvement from 25.45% baseline)

### Coverage by Component
| Component | Coverage | Status |
|-----------|----------|--------|
| CrewAI Agents | 100% | ✅ Excellent |
| Error Handlers | 82.35% | ✅ Very Good |
| Logging Config | 90.91% | ✅ Excellent |
| Hybrid Search Tools | 48.06% | ⚡ Good |
| Database Handler | 28.21% | 💡 Needs Improvement |
| Batch Processor | 17.88% | 💡 Needs Improvement |

## 🏗️ Test Architecture

### Directory Structure
```
tests/
├── conftest.py              # Shared fixtures and configuration
├── test_setup.py           # Setup verification tests
├── unit/                   # Unit tests (isolated components)
│   ├── test_agents.py      # CrewAI agents (26 tests)
│   ├── test_tools.py       # Search tools (25 tests)
│   ├── test_batch_error_logging.py  # Batch/error/logging (28 tests)
│   ├── test_batch_processing.py     # Batch processing (39 tests)
│   └── test_database.py    # Database operations (30 tests)
├── integration/            # Integration tests (component interactions)
│   └── test_workflow.py    # Workflow orchestration (19 tests)
└── e2e/                   # End-to-end tests (complete pipeline)
    └── test_full_mapping_flow.py    # Full pipeline (19 tests)
```

### Test Configuration
```ini
# pytest.ini
[pytest]
testpaths = tests
addopts = 
    -v --strict-markers --tb=short
    --cov=crew_components --cov=batch_processor --cov=auto_mapping
    --cov-report=term-missing --cov-report=html --cov-report=xml

markers =
    unit: Unit tests
    integration: Integration tests  
    e2e: End-to-end tests
    slow: Slow running tests
    database: Tests requiring database connection
    mock: Tests using mocked dependencies
```

## 🔧 Test Fixtures

### Shared Fixtures (conftest.py)
```python
# Database fixtures
@pytest.fixture
def mock_db_connection():
    """Mock database connection for testing."""
    
@pytest.fixture  
def mock_db_pool():
    """Mock database connection pool."""

# Agent fixtures
@pytest.fixture
def mock_mapper_agent():
    """Mock Mapper agent with proper BaseTool mocking."""
    
@pytest.fixture
def mock_validation_agent():
    """Mock Validation agent."""

# Tool fixtures
@pytest.fixture
def mock_pg_search_tool():
    """Mock PGSearchTool using proper BaseTool inheritance."""
    
@pytest.fixture
def sample_category_metadata():
    """Sample category metadata for testing."""

# Data fixtures
@pytest.fixture
def sample_product_data():
    """Sample product data with edge cases."""
    
@pytest.fixture
def expected_mapping_results():
    """Expected mapping results for validation."""
```

## 🎯 Unit Tests

### Agent Tests (26 tests - 100% success)
```python
# Test agent creation and configuration
def test_mapper_agent_creation():
    assert isinstance(mapper_agent, Agent)
    assert mapper_agent.role == 'Product Category Mapper'
    assert 'hierarchy' in mapper_agent.goal

# Test tool assignment
def test_agent_tool_assignment(mock_pg_search_tool):
    test_agent = Agent(
        role=mapper_agent.role,
        tools=[mock_pg_search_tool]
    )
    assert len(test_agent.tools) == 1

# Test agent execution with mocking
@patch('crewai.Agent.execute_task')
def test_agent_execution_mock(mock_execute):
    mock_execute.return_value = json.dumps(expected_output)
    result = mapper_agent.execute_task(task)
    assert result == expected_output
```

### Tool Tests (20/22 passing - 91% success)
```python
# Test hybrid search result scoring
def test_hybrid_search_result_creation():
    result = HybridSearchResult(
        category=category,
        keyword_score=0.8,
        semantic_score=0.9
    )
    assert abs(result.total_score - 0.86) < 0.001

# Test search tool with proper mocking
def test_search_tool_with_mock(mock_pg_search_tool):
    tool = CategoryHybridSearchTool()
    result = tool._run("test query")
    assert isinstance(result, str)
```

### Error Handling Tests (24/28 passing - 86% success)
```python
# Test error class hierarchy
def test_error_hierarchy():
    assert issubclass(ToolExecutionError, MappingError)
    assert issubclass(AgentExecutionError, MappingError)

# Test protected execution
@patch('crew_components.error_handlers.logger')
def test_protected_execution_success(mock_logger):
    with ProtectedExecution("test_op", ErrorCategory.TOOL) as protected:
        protected.set_result("success")
    assert protected.result == "success"
```

## 🔗 Integration Tests

### Workflow Integration (12/19 passing - 63% success)
```python
# Test agent-tool integration
@patch('crew_components.agents.PGSearchTool')
def test_agent_tool_integration(mock_tool, mock_pg_search_tool):
    mock_tool.return_value = mock_pg_search_tool
    
    # Assign tool to agent
    test_agent = mapper_agent
    test_agent.tools = [mock_pg_search_tool]
    
    # Verify integration
    assert len(test_agent.tools) == 1

# Test crew workflow
@patch('crewai.Crew.kickoff')
def test_crew_workflow_execution(mock_kickoff):
    mock_result = MagicMock()
    mock_result.raw = json.dumps(expected_result)
    mock_kickoff.return_value = mock_result
    
    result = category_mapping_crew.kickoff(inputs=test_inputs)
    assert result == mock_result
```

### Database Integration
```python
# Test database workflow integration
@patch('crew_components.database_handler.psycopg2.connect')
def test_database_workflow_integration(mock_connect):
    mock_conn = MagicMock()
    mock_connect.return_value = mock_conn
    
    with DatabaseHandler() as db_handler:
        assert db_handler.conn == mock_conn
```

## 🌐 End-to-End Tests

### Complete Pipeline Tests (15/16 passing - 94% success)
```python
# Test complete mapping flow
@patch('auto_mapping.CategoryHybridSearchTool')
@patch('auto_mapping.DatabaseHandler')
def test_single_product_mapping_success_flow(mock_db, mock_search):
    product_data = {
        "product_id": "E2E_TEST_001",
        "product_name": "Dell XPS 13 Laptop",
        "breadcrumbs": "Electronics > Computers > Laptops"
    }
    
    result = process_single_product(product_data)
    
    if result:
        assert "product_id" in result
        assert result["product_id"] == "E2E_TEST_001"

# Test error scenarios
def test_missing_breadcrumbs_scenario():
    product_no_breadcrumbs = {
        "product_id": "NO_BREADCRUMBS_001",
        "breadcrumbs": None,
        "product_info": "Premium organic green tea"
    }
    
    # Should handle gracefully with lower confidence
    expected_mapping = {
        "confidence": 0.75,  # Lower due to missing breadcrumbs
        "status": "warning"
    }
```

## 🏃‍♂️ Running Tests

### Basic Test Execution
```bash
# Run all tests
pytest

# Run with verbose output
pytest -v

# Run specific test categories
pytest -m unit
pytest -m integration  
pytest -m e2e

# Run specific test files
pytest tests/unit/test_agents.py
pytest tests/integration/test_workflow.py
```

### Coverage Analysis
```bash
# Generate coverage report
pytest --cov

# Generate HTML coverage report
pytest --cov --cov-report=html
# Open htmlcov/index.html

# Coverage with missing lines
pytest --cov --cov-report=term-missing
```

### Parallel Test Execution
```bash
# Run tests in parallel (faster)
pytest -n auto  # Auto-detect CPU cores
pytest -n 4     # Use 4 workers

# Parallel with coverage
pytest -n 4 --cov
```

### Performance Testing
```bash
# Run performance tests (marked as slow)
pytest -m slow

# Skip slow tests during development
pytest -m "not slow"

# Run database tests (requires DB connection)
pytest -m database

# Skip database tests
pytest -m "not database"
```

## 🔍 Test Development

### Writing Unit Tests
```python
# Follow AAA pattern: Arrange, Act, Assert
def test_example_function():
    # Arrange
    input_data = {"key": "value"}
    expected_result = {"output": "expected"}
    
    # Act
    result = function_under_test(input_data)
    
    # Assert
    assert result == expected_result
```

### Using Fixtures
```python
def test_with_fixtures(sample_product_data, mock_db_connection):
    """Test using shared fixtures from conftest.py."""
    assert len(sample_product_data) == 3
    mock_db_connection.cursor.assert_called()
```

### Mocking External Dependencies
```python
@patch('crew_components.tools.category_hybrid_search.CategoryHybridSearch')
def test_with_mock(mock_search_class):
    mock_instance = MagicMock()
    mock_search_class.return_value = mock_instance
    
    # Test with mocked dependency
    result = function_using_search()
    assert result == expected
```

### Testing Async Operations
```python
@pytest.mark.asyncio
async def test_async_operation():
    result = await async_function()
    assert result is not None
```

## 📋 Test Maintenance

### Adding New Tests
1. **Identify Component:** Determine test category (unit/integration/e2e)
2. **Create Test File:** Follow naming convention `test_[component].py`
3. **Use Fixtures:** Leverage existing fixtures from `conftest.py`
4. **Add Markers:** Use appropriate pytest markers
5. **Update Coverage:** Ensure new code is tested

### Test Quality Guidelines
1. **Descriptive Names:** Clear test function names
2. **Single Responsibility:** One assertion per test
3. **Proper Mocking:** Mock external dependencies
4. **Edge Cases:** Test error conditions and edge cases
5. **Performance:** Keep tests fast (<1s each)

### Continuous Integration
```yaml
# GitHub Actions example
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - run: pip install -r requirements.txt -r test-requirements.txt
      - run: pytest --cov --cov-report=xml
      - uses: codecov/codecov-action@v2
```

## 🚨 Debugging Tests

### Common Test Issues
1. **Import Errors:** Check PYTHONPATH and module structure
2. **Fixture Errors:** Verify fixture dependencies and scope
3. **Mock Issues:** Ensure proper mock setup and teardown
4. **Database Tests:** Check test database configuration
5. **Slow Tests:** Use `-m "not slow"` during development

### Debug Tools
```bash
# Verbose output with full tracebacks
pytest -vv --tb=long

# Drop into debugger on failure
pytest --pdb

# Run single test with debugging
pytest tests/unit/test_agents.py::test_specific_function -vv --tb=long

# Check test discovery
pytest --collect-only
```

### Test Performance
```bash
# Measure test execution time
pytest --durations=10  # Show 10 slowest tests

# Profile test memory usage
pytest --memray

# Run tests with timeout
pytest --timeout=30  # 30 second timeout per test
```

## 📈 Test Metrics & Goals

### Current Test Metrics
- **Unit Tests:** 138 tests, 74% success rate
- **Integration Tests:** 19 tests, 63% success rate  
- **E2E Tests:** 19 tests, 94% success rate
- **Performance Tests:** 3 tests (skipped by default)

### Coverage Goals
- **Overall Coverage:** Target >50% (current: 37.53%)
- **Core Components:** Target >80% (agents: 100% ✅)
- **Critical Paths:** Target >90% (error handling: 82% ✅)
- **New Code:** Require >70% coverage for new features

### Quality Metrics
- **Test Success Rate:** Target >90% (current: 74%)
- **Test Execution Time:** Target <60s total (current: ~67s)
- **Test Maintenance:** Regular review and updates
- **Documentation Coverage:** All tests documented

## 🔄 Test Workflow

### Development Workflow
1. **Write Tests First:** TDD approach for new features
2. **Run Tests Locally:** Before committing changes
3. **Check Coverage:** Ensure adequate test coverage
4. **Update Documentation:** Keep test docs current
5. **Review Tests:** Peer review for test quality

### Continuous Testing
```bash
# Watch mode for development
pytest-watch

# Pre-commit hook
pytest --maxfail=1 --disable-warnings

# CI/CD pipeline
pytest --cov --cov-fail-under=70
```

## 🛠️ Test Utilities

### Custom Assertions
```python
@pytest.fixture
def assert_mapping_result():
    """Custom assertion for mapping results."""
    def _assert(result: Dict[str, Any], expected: Dict[str, Any]):
        assert result["product_id"] == expected["product_id"]
        assert result["status"] in ["success", "warning", "error"]
        assert 0 <= result.get("confidence", 0) <= 1
    return _assert
```

### Test Data Generators
```python
def generate_test_products(count: int = 10) -> List[Dict]:
    """Generate test product data."""
    return [
        {
            "product_id": f"TEST_{i:03d}",
            "product_name": f"Test Product {i}",
            "breadcrumbs": f"Category {i % 5} > Subcategory {i % 3}",
            "product_info": f"Test product information {i}"
        }
        for i in range(count)
    ]
```

### Performance Test Helpers
```python
@pytest.fixture
def benchmark_timer():
    """Timer for performance testing."""
    class Timer:
        def __init__(self):
            self.times = []
        
        def time_operation(self, func, *args, **kwargs):
            start = time.time()
            result = func(*args, **kwargs)
            elapsed = time.time() - start
            self.times.append(elapsed)
            return result
    
    return Timer()
```

---

For more information, see [API Reference](api_reference.md) and [Performance Guide](performance.md).

