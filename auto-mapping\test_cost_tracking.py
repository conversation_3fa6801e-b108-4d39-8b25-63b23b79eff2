#!/usr/bin/env python3
"""
Test Script for Cost Tracking Integration

This script tests the cost tracking functionality in the CrewAI category mapping system
including individual product costs and batch cost summaries.
"""

import sys
import os
import json
import time
import asyncio
from typing import Dict, List

# Add the current directory to sys.path
sys.path.append(os.path.dirname(__file__))

def test_cost_tracker():
    """Test the basic cost tracking functionality."""
    print("Testing CostTracker basic functionality...")
    
    try:
        from crew_components.cost_tracker import CostTracker, estimate_processing_cost
        
        # Initialize tracker
        tracker = CostTracker()
        
        # Test semantic search tracking
        tracker.track_semantic_search("organic tomato seeds")
        tracker.track_semantic_search("garden tools spade")
        
        # Test mapper operation tracking
        input_text = '{"product_info": {"product_name": "Organic Seeds", "description": "Premium seeds"}}'
        output_text = '{"level_1": "Garden & DIY", "level_2": "Seeds"}'
        tracker.track_mapper_operation(input_text, output_text)
        
        # Test validation operation tracking
        val_input = '{"product_info": "...", "mapper_output": "..."}'
        val_output = '{"level_1": "Garden & DIY", "confidence_score": 0.95}'
        tracker.track_validation_operation(val_input, val_output)
        
        # Test retry tracking
        tracker.track_retry_attempt()
        
        # Finalize costs
        final_costs = tracker.finalize_costs()
        
        print(f"[OK] Total cost: ${final_costs.total_cost:.4f}")
        print(f"[OK] Total tokens: {final_costs.total_tokens:,}")
        print(f"[OK] Mapper cost: ${final_costs.mapper_cost:.4f}")
        print(f"[OK] Validation cost: ${final_costs.validation_cost:.4f}")
        print(f"[OK] Search cost: ${final_costs.semantic_search_cost:.4f}")
        print(f"[OK] Retry count: {final_costs.retry_count}")
        
        # Test cost estimation
        estimate = estimate_processing_cost(10, 400)
        print(f"\n[OK] Estimated cost for 10 products: ${estimate['estimated_total_cost']}")
        print(f"[OK] Cost per product: ${estimate['cost_per_product']}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Cost tracker test failed: {e}")
        return False

def test_output_schemas():
    """Test the enhanced output schemas with cost tracking."""
    print("\nTesting enhanced output schemas...")
    
    try:
        from crew_components.output_schemas import CostTracking, ProcessingResult, ValidationResult
        
        # Test CostTracking schema
        cost_data = CostTracking(
            total_cost=0.0145,
            total_tokens=2340,
            mapper_cost=0.008,
            validation_cost=0.0065,
            retry_count=1
        )
        
        print(f"[OK] CostTracking schema: ${cost_data.total_cost:.4f}")
        
        # Test ProcessingResult schema
        val_result = ValidationResult(
            level_1="Garden & DIY",
            level_2="Seeds",
            level_3="Vegetable Seeds",
            level_4="",
            level_5="",
            level_6="",
            level_7="",
            confidence_score=0.95
        )
        
        processing_result = ProcessingResult(
            product_id=12345,
            status="success",
            processing_time=2.35,
            final_result=val_result,
            cost_tracking=cost_data,
            search_queries_used=["organic seeds", "vegetable seeds"],
            categories_found=15,
            retry_attempts=1
        )
        
        print(f"[OK] ProcessingResult schema created for product {processing_result.product_id}")
        print(f"[OK] Processing time: {processing_result.processing_time:.2f}s")
        print(f"[OK] Categories found: {processing_result.categories_found}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Output schemas test failed: {e}")
        return False

async def test_async_processor_integration():
    """Test cost tracking integration with async_processor."""
    print("\nTesting async_processor cost tracking integration...")
    
    try:
        from async_processor import run_async_processing_with_search_tool
        
        # Sample test products
        test_products = [
            {
                'product_id': 1001,
                'product_name': 'Organic Tomato Seeds',
                'product_description': 'Premium organic tomato seeds for home gardening',
                'breadcrumbs': '',
                'url': ''
            },
            {
                'product_id': 1002,
                'product_name': 'Garden Spade Tool',
                'product_description': 'Heavy-duty steel spade for digging and planting',
                'breadcrumbs': '',
                'url': ''
            }
        ]
        
        print(f"Processing {len(test_products)} test products with cost tracking...")
        
        # Run processing with cost tracking
        start_time = time.time()
        stats = await asyncio.to_thread(
            run_async_processing_with_search_tool,
            test_products,
            max_concurrent=2,
            max_retries=1  # Reduced for testing
        )
        
        processing_duration = time.time() - start_time
        
        # Validate cost tracking in results
        print(f"\n[OK] Processing completed in {processing_duration:.2f}s")
        print(f"[OK] Total products: {stats['total']}")
        print(f"[OK] Successful: {stats['successful']}")
        
        # Check cost summary
        if 'cost_summary' in stats:
            cost_summary = stats['cost_summary']
            print(f"[OK] Total cost: ${cost_summary['total_cost']:.4f}")
            print(f"[OK] Total tokens: {cost_summary['total_tokens']:,}")
            print(f"[OK] Cost per product: ${cost_summary['cost_per_product']:.4f}")
            print(f"[OK] Search queries: {cost_summary['search_queries_count']}")
            print(f"[OK] Retries: {cost_summary['total_retries']}")
        
        # Check individual results
        for i, result in enumerate(stats['results']):
            if 'cost_tracking' in result:
                cost_data = result['cost_tracking']
                print(f"[OK] Product {result.get('product_id', i)}: ${cost_data['total_cost']:.4f}")
            
        return True
        
    except Exception as e:
        print(f"[ERROR] Async processor integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backend_routes():
    """Test the backend routes with cost tracking."""
    print("\nTesting backend routes cost tracking...")
    
    try:
        # Import the conversion function
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
        from routes import Product, convert_crewai_result_to_response
        
        # Mock product
        test_product = Product(
            product_id=12345,
            title="Test Product",
            description="Test product description"
        )
        
        # Mock CrewAI result with cost tracking
        mock_result = {
            'status': 'success',
            'result': {
                'level_1': 'Garden & DIY',
                'level_2': 'Seeds',
                'level_3': 'Vegetable Seeds',
                'level_4': '',
                'level_5': '',
                'level_6': '',
                'level_7': '',
                'confidence_score': 0.95
            },
            'cost_tracking': {
                'total_cost': 0.0125,
                'total_tokens': 1850,
                'mapper_cost': 0.007,
                'validation_cost': 0.0055,
                'semantic_search_cost': 0.0001,
                'retry_cost': 0.0,
                'retry_count': 0
            },
            'processing_time': 3.2,
            'retry_attempts': 0,
            'search_queries_used': ['seeds', 'vegetable seeds', 'tomato']
        }
        
        # Test conversion
        response = convert_crewai_result_to_response(test_product, mock_result)
        
        print(f"[OK] Product ID: {response.product_id}")
        print(f"[OK] Category: {response.level_1} > {response.level_2} > {response.level_3}")
        print(f"[OK] Confidence: {response.confidence_score}")
        
        if response.cost_info:
            print(f"[OK] Cost info included: ${response.cost_info.total_cost:.4f}")
            print(f"[OK] Tokens: {response.cost_info.total_tokens:,}")
            print(f"[OK] Processing time: {response.cost_info.processing_time:.2f}s")
            print(f"[OK] Retries: {response.cost_info.retry_attempts}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Backend routes test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cost_estimation():
    """Test cost estimation functionality."""
    print("\nTesting cost estimation...")
    
    try:
        from crew_components.cost_tracker import estimate_processing_cost
        
        # Test different batch sizes
        test_cases = [
            (1, 300),    # Single short product
            (10, 500),   # Small batch, medium length
            (100, 400),  # Large batch, short length
            (50, 800)    # Medium batch, long descriptions
        ]
        
        for product_count, avg_length in test_cases:
            estimate = estimate_processing_cost(product_count, avg_length)
            
            print(f"[OK] {product_count} products (avg {avg_length} chars):")
            print(f"  Total cost: ${estimate['estimated_total_cost']}")
            print(f"  Cost per product: ${estimate['cost_per_product']}")
            print(f"  Estimated tokens: {estimate['estimated_tokens']:,}")
            
        return True
        
    except Exception as e:
        print(f"[ERROR] Cost estimation test failed: {e}")
        return False

async def main():
    """Run all cost tracking tests."""
    print("=" * 60)
    print("Cost Tracking Integration Tests")
    print("=" * 60)
    
    tests = [
        ("Basic Cost Tracker", test_cost_tracker),
        ("Output Schemas", test_output_schemas),
        ("Cost Estimation", test_cost_estimation),
        ("Backend Routes", test_backend_routes),
        ("Async Processor Integration", test_async_processor_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                success = await test_func()
            else:
                success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"[ERROR] {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("COST TRACKING TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "[PASS]" if success else "[FAIL]"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n[SUCCESS] All cost tracking tests passed! 💰")
        print("The system now tracks:")
        print("- Individual AI operation costs (mapper, validator, search)")
        print("- Token usage for each operation")
        print("- Processing time and retry attempts")
        print("- Aggregate cost summaries for batches")
        print("- Cost estimation for future batches")
    else:
        print("\n[WARNING] Some tests failed. Check the issues above.")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(main())