"""
Semantic Category Search Tool

Performs semantic search on category embeddings for accurate category matching.
"""

import os
from typing import List, Dict, Optional, Tuple
from .category_embeddings_manager import CategoryEmbeddingsManager
from crewai.tools import BaseTool
from pydantic import BaseModel, Field


class SemanticSearchResult(BaseModel):
    """Represents a single search result from semantic search."""
    category: Dict
    score: float

    def __repr__(self):
        path = self.category.get('path', 'N/A')
        return f"SemanticSearchResult(path='{path}', score={self.score:.4f})"


class CategorySemanticSearch:
    """
    Performs semantic search on category embeddings.
    """
    def __init__(self):
        """Initialize the semantic search manager."""
        print("Initializing Semantic Search...")
        # Semantic search (optional)
        self.embeddings_manager = CategoryEmbeddingsManager()
        self.semantic_available = self.embeddings_manager.embeddings is not None
        
        if self.semantic_available:
            stats = self.embeddings_manager.get_statistics()
            print(f"  [OK] Semantic search ready ({stats['num_embeddings']} embeddings)")
        else:
            print("  [ERROR] Semantic search not available. Embeddings file not found.")
            print("        Run 'python generate_embeddings.py' to enable semantic search.")
            # This should be a critical failure.
            raise FileNotFoundError("Embeddings file not found. Cannot initialize CategorySemanticSearch.")

    def search(self, query: str, k: int = 10) -> List[SemanticSearchResult]:
        """
        Perform semantic search.
        
        Args:
            query: Search query
            k: Number of results to return
            
        Returns:
            A list of SemanticSearchResult objects.
        """
        if not self.semantic_available:
            return []

        # Perform semantic search
        semantic_results = self.embeddings_manager.search_similar(query, k=k)

        # Convert to SemanticSearchResult objects
        results = [
            SemanticSearchResult(category=cat, score=score)
            for cat, score in semantic_results
        ]
        
        # Sort by score
        results.sort(key=lambda x: x.score, reverse=True)
        
        return results

    def format_results(self, results: List[SemanticSearchResult], query: str) -> str:
        """Format search results for display."""
        if not results:
            return f"No results found for query: '{query}'"

        formatted_list = []
        for i, res in enumerate(results):
            path = res.category.get('full_path', res.category.get('path', 'N/A'))
            formatted_list.append(f"  {i+1}. Path: \"{path}\" (Score: {res.score:.4f})")
        
        return (
            f"Search results for query: '{query}'\n"
            f"--------------------------------------\n"
            + "\n".join(formatted_list)
            + "\n--------------------------------------"
        )

class CategorySemanticSearchTool(BaseTool):
    name: str = "category_semantic_search"
    description: str = (
        "Performs a semantic search against a category database to find the most relevant category paths. "
        "Input must be a simple string query. "
        "Example: category_semantic_search(query='organic tomato seeds')"
    )
    search_tool: CategorySemanticSearch = Field(default_factory=CategorySemanticSearch)
    
    def _run(self, query: str) -> str:
        """
        Run the semantic search and return formatted results.
        
        Args:
            query: The search query string.
        
        Returns:
            A formatted string of search results.
        """
        # Track this search query globally if cost tracker is available
        # This is a simple way to track queries without changing the interface
        try:
            # Check if there's a global cost tracker in the current context
            import threading
            current_thread = threading.current_thread()
            if hasattr(current_thread, 'cost_tracker'):
                current_thread.cost_tracker.track_semantic_search(query)
            if hasattr(current_thread, 'search_queries'):
                current_thread.search_queries.append(query)
        except Exception:
            # If tracking fails, continue silently
            pass
            
        results = self.search_tool.search(query, k=10)
        return self.search_tool.format_results(results, query)


def test_semantic_search():
    """Test the semantic search tool."""
    print("=" * 60)
    print("Testing Category Semantic Search Tool")
    print("=" * 60)
    
    try:
        # Initialize the tool
        tool = CategorySemanticSearchTool()

        # Test query
        query = "garden tools"
        print(f"\nSearching for: '{query}'")
        
        # Run the tool
        output = tool.run(query)
        print(output)

    except Exception as e:
        print(f"\n[ERROR] Test failed: {e}")


if __name__ == "__main__":
    test_semantic_search()
