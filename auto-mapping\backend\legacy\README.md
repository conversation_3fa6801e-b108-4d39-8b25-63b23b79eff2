# Legacy Backend Files

This directory contains deprecated backend files that were replaced during the CrewAI integration.

## Files

### `category_generator_legacy_DEPRECATED.py`
- **Original**: `category_generator.py` (816 lines)
- **Description**: Legacy OpenAI-based category mapping system
- **Replaced by**: CrewAI `async_processor.py` with semantic search and retry logic
- **Status**: Deprecated - Do not use in production

### `routes_legacy_backup.py` 
- **Original**: `routes.py`
- **Description**: Original FastAPI routes using legacy `category_generator`
- **Replaced by**: New `routes.py` with CrewAI integration
- **Status**: Backup only - For reference if needed

## Why These Were Replaced

### Issues with Legacy System:
1. **Sequential Processing**: Processed products one by one
2. **Complex OpenAI Workflows**: Heavy embedding calculations per product
3. **No Retry Logic**: Failed requests had no recovery mechanism
4. **Limited Concurrency**: Could not handle multiple products efficiently
5. **Complex Hard/Soft Logic**: Intricate rule-based processing

### Benefits of New CrewAI System:
1. **Concurrent Processing**: 3 products processed simultaneously
2. **FAISS Semantic Search**: Fast similarity search with 21K+ embeddings
3. **Intelligent Retry**: Exponential backoff with feedback learning
4. **Explicit Passthrough**: Direct mapper → validation flow
5. **Better Error Handling**: Comprehensive error management and logging

## Integration Date
**Replaced on**: 2025-01-09
**Integration Summary**: See `../INTEGRATION_SUMMARY.md`

---

⚠️ **Warning**: These files are kept for historical reference only. Do not use in production systems.