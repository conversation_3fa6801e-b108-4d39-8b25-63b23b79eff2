# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node modules
node_modules/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp

# Product search results (if they contain sensitive data)
product-search-results/

# Cache
.cache/

# Exclude specific folders
on-demand-scrapping/

# AI/Development Tools
.claude/
.cursor/
.taskmaster/
.cursorignore

# Project Requirements (may contain sensitive info)
prd.md